# Development Guide

Guidelines and best practices for developing AIntegrity.

## Getting Started

### Prerequisites
- Node.js 18+
- PostgreSQL (local or hosted)
- Git
- Code editor (VS Code recommended)

### Initial Setup
1. Follow the [Setup Guide](./SETUP.md)
2. Install recommended VS Code extensions:
   - Prisma
   - Tailwind CSS IntelliSense
   - ES7+ React/Redux/React-Native snippets

### Development Workflow

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write code following the style guide
   - Add tests for new functionality
   - Update documentation if needed

3. **Test Changes**
   ```bash
   npm run lint
   npm run build
   npm run test  # when tests are added
   ```

4. **Commit Changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

## Code Style Guide

### TypeScript
- Use TypeScript for all new code
- Define interfaces for all data structures
- Use strict type checking
- Prefer `interface` over `type` for object shapes

```typescript
// Good
interface User {
  id: string
  name: string
  email: string
}

// Avoid
type User = {
  id: string
  name: string
  email: string
}
```

### React Components
- Use functional components with hooks
- Prefer named exports
- Use TypeScript interfaces for props
- Keep components small and focused

```typescript
// Good
interface ButtonProps {
  children: React.ReactNode
  onClick: () => void
  variant?: 'primary' | 'secondary'
}

export function Button({ children, onClick, variant = 'primary' }: ButtonProps) {
  return (
    <button 
      onClick={onClick}
      className={cn('btn', `btn-${variant}`)}
    >
      {children}
    </button>
  )
}
```

### File Naming
- Use kebab-case for files: `user-profile.tsx`
- Use PascalCase for components: `UserProfile`
- Use camelCase for functions and variables
- Use UPPER_CASE for constants

### Import Organization
```typescript
// 1. React and Next.js imports
import { useState } from 'react'
import { useRouter } from 'next/navigation'

// 2. Third-party libraries
import { Button } from '@/components/ui/button'

// 3. Internal imports
import { prisma } from '@/lib/db'
import { authOptions } from '@/lib/auth'

// 4. Type imports (separate)
import type { User } from '@prisma/client'
```

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Route groups
│   ├── api/               # API routes
│   ├── dashboard/         # Dashboard pages
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # ShadCN/UI components
│   ├── dashboard/        # Dashboard-specific components
│   └── providers/        # Context providers
├── lib/                  # Utility functions
│   ├── auth.ts          # Authentication config
│   ├── db.ts            # Database connection
│   ├── openai.ts        # OpenAI integration
│   └── utils.ts         # General utilities
└── types/               # TypeScript type definitions
    └── index.ts
```

## Database Development

### Schema Changes
1. **Modify Prisma Schema**
   ```bash
   # Edit prisma/schema.prisma
   ```

2. **Generate Migration**
   ```bash
   npm run db:migrate
   ```

3. **Update Prisma Client**
   ```bash
   npm run db:generate
   ```

### Best Practices
- Always create migrations for schema changes
- Use descriptive migration names
- Test migrations on development data
- Add database indexes for frequently queried fields

### Seeding Data
Create `prisma/seed.ts` for development data:

```typescript
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Create test users, notes, etc.
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
```

## API Development

### Creating New Endpoints

1. **Create Route Handler**
   ```typescript
   // src/app/api/example/route.ts
   import { NextRequest, NextResponse } from 'next/server'
   import { getServerSession } from 'next-auth'
   import { authOptions } from '@/lib/auth'

   export async function GET(req: NextRequest) {
     const session = await getServerSession(authOptions)
     
     if (!session) {
       return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
     }

     // Your logic here
     return NextResponse.json({ data: 'example' })
   }
   ```

2. **Add Error Handling**
   ```typescript
   try {
     // Your logic
   } catch (error) {
     console.error('API Error:', error)
     return NextResponse.json(
       { error: 'Internal server error' },
       { status: 500 }
     )
   }
   ```

3. **Validate Input**
   ```typescript
   const { title, content } = await req.json()
   
   if (!title || !content) {
     return NextResponse.json(
       { error: 'Title and content are required' },
       { status: 400 }
     )
   }
   ```

## Component Development

### Creating New Components

1. **Create Component File**
   ```typescript
   // src/components/example-component.tsx
   'use client'  // Only if using client-side features

   interface ExampleComponentProps {
     title: string
     onAction: () => void
   }

   export function ExampleComponent({ title, onAction }: ExampleComponentProps) {
     return (
       <div>
         <h2>{title}</h2>
         <button onClick={onAction}>Action</button>
       </div>
     )
   }
   ```

2. **Add to Index (if needed)**
   ```typescript
   // src/components/index.ts
   export { ExampleComponent } from './example-component'
   ```

### Using ShadCN/UI Components

1. **Install Component**
   ```bash
   npx shadcn@latest add button
   ```

2. **Use in Your Component**
   ```typescript
   import { Button } from '@/components/ui/button'

   export function MyComponent() {
     return <Button variant="outline">Click me</Button>
   }
   ```

## Testing

### Unit Tests (Future)
```typescript
// __tests__/utils.test.ts
import { describe, it, expect } from 'vitest'
import { formatDate } from '@/lib/utils'

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2024-01-01')
    expect(formatDate(date)).toBe('Jan 1, 2024')
  })
})
```

### Integration Tests (Future)
```typescript
// __tests__/api/notes.test.ts
import { describe, it, expect } from 'vitest'
import { POST } from '@/app/api/notes/route'

describe('/api/notes', () => {
  it('should create note', async () => {
    // Test API endpoint
  })
})
```

## Debugging

### Database Issues
```bash
# View database in browser
npm run db:studio

# Reset database
npm run db:push --force-reset
```

### API Issues
- Check browser Network tab
- Add console.log statements
- Use Postman/Insomnia for API testing

### Build Issues
```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules
rm -rf node_modules package-lock.json
npm install
```

## Performance

### Frontend Optimization
- Use React.memo for expensive components
- Implement proper loading states
- Use Next.js Image component for images
- Minimize bundle size with dynamic imports

### Backend Optimization
- Use database indexes
- Implement proper caching
- Optimize Prisma queries
- Use connection pooling

## Security

### Best Practices
- Validate all user inputs
- Use environment variables for secrets
- Implement proper authentication checks
- Sanitize data before database operations
- Use HTTPS in production

### Common Vulnerabilities
- SQL injection (prevented by Prisma)
- XSS attacks (sanitize user content)
- CSRF attacks (use proper CORS settings)
- Unauthorized access (check user permissions)

## Deployment

See [DEPLOYMENT.md](./DEPLOYMENT.md) for production deployment guidelines.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests (when available)
5. Update documentation
6. Submit a pull request

### Pull Request Guidelines
- Use descriptive titles
- Include summary of changes
- Reference related issues
- Ensure all checks pass
- Request review from maintainers

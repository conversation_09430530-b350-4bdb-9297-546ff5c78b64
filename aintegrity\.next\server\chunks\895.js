"use strict";exports.id=895,exports.ids=[895],exports.modules={40276:(e,t,s)=>{var n,r,i,a,o,l,c,u,h,d,f,p,m,g,y,_,w,b,v,x,S,$,A,I,O,R,k,E,P,C,T,N,M,j,L,D,B,W,U,q,F,X,J,H,K,V,z,Q,G,Y,Z,ee,et,es,en,er,ei,ea,eo,el,ec,eu,eh,ed,ef,ep,em,eg,ey,e_;let ew,eb,ev;function ex(e,t,s,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s}function eS(e,t,s,n){if("a"===s&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}s.d(t,{Ay:()=>s3});let e$=function(){let{crypto:e}=globalThis;if(e?.randomUUID)return e$=e.randomUUID.bind(e),e.randomUUID();let t=new Uint8Array(1),s=e?()=>e.getRandomValues(t)[0]:()=>255*Math.random()&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^s()&15>>e/4).toString(16))};function eA(e){return"object"==typeof e&&null!==e&&("name"in e&&"AbortError"===e.name||"message"in e&&String(e.message).includes("FetchRequestCanceledException"))}let eI=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e){try{if("[object Error]"===Object.prototype.toString.call(e)){let t=Error(e.message,e.cause?{cause:e.cause}:{});return e.stack&&(t.stack=e.stack),e.cause&&!t.cause&&(t.cause=e.cause),e.name&&(t.name=e.name),t}}catch{}try{return Error(JSON.stringify(e))}catch{}}return Error(e)};class eO extends Error{}class eR extends eO{constructor(e,t,s,n){super(`${eR.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.requestID=n?.get("x-request-id"),this.error=t,this.code=t?.code,this.param=t?.param,this.type=t?.type}static makeMessage(e,t,s){let n=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new eE({message:s,cause:eI(t)});let r=t?.error;return 400===e?new eC(e,r,s,n):401===e?new eT(e,r,s,n):403===e?new eN(e,r,s,n):404===e?new eM(e,r,s,n):409===e?new ej(e,r,s,n):422===e?new eL(e,r,s,n):429===e?new eD(e,r,s,n):e>=500?new eB(e,r,s,n):new eR(e,r,s,n)}}class ek extends eR{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class eE extends eR{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class eP extends eE{constructor({message:e}={}){super({message:e??"Request timed out."})}}class eC extends eR{}class eT extends eR{}class eN extends eR{}class eM extends eR{}class ej extends eR{}class eL extends eR{}class eD extends eR{}class eB extends eR{}class eW extends eO{constructor(){super("Could not parse response content as the length limit was reached")}}class eU extends eO{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}let eq=/^[a-z][a-z0-9+.-]*:/i,eF=e=>eq.test(e);function eX(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}let eJ=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new eO(`${e} must be an integer`);if(t<0)throw new eO(`${e} must be a positive integer`);return t},eH=e=>{try{return JSON.parse(e)}catch(e){return}},eK=e=>new Promise(t=>setTimeout(t,e)),eV={off:0,error:200,warn:300,info:400,debug:500},ez=(e,t,s)=>{if(e){if(Object.prototype.hasOwnProperty.call(eV,e))return e;e0(s).warn(`${t} was set to ${JSON.stringify(e)}, expected one of ${JSON.stringify(Object.keys(eV))}`)}};function eQ(){}function eG(e,t,s){return!t||eV[e]>eV[s]?eQ:t[e].bind(t)}let eY={error:eQ,warn:eQ,info:eQ,debug:eQ},eZ=new WeakMap;function e0(e){let t=e.logger,s=e.logLevel??"off";if(!t)return eY;let n=eZ.get(t);if(n&&n[0]===s)return n[1];let r={error:eG("error",t,s),warn:eG("warn",t,s),info:eG("info",t,s),debug:eG("debug",t,s)};return eZ.set(t,[s,r]),r}let e1=e=>(e.options&&(e.options={...e.options},delete e.options.headers),e.headers&&(e.headers=Object.fromEntries((e.headers instanceof Headers?[...e.headers]:Object.entries(e.headers)).map(([e,t])=>[e,"authorization"===e.toLowerCase()||"cookie"===e.toLowerCase()||"set-cookie"===e.toLowerCase()?"***":t]))),"retryOfRequestLogID"in e&&(e.retryOfRequestLogID&&(e.retryOf=e.retryOfRequestLogID),delete e.retryOfRequestLogID),e),e2="5.3.0",e4=()=>"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator,e3=()=>{let e="undefined"!=typeof Deno&&null!=Deno.build?"deno":"undefined"!=typeof EdgeRuntime?"edge":"[object process]"===Object.prototype.toString.call(void 0!==globalThis.process?globalThis.process:0)?"node":"unknown";if("deno"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":e6(Deno.build.os),"X-Stainless-Arch":e8(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:Deno.version?.deno??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if("node"===e)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":e6(globalThis.process.platform??"unknown"),"X-Stainless-Arch":e8(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};let t=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let s=t.exec(navigator.userAgent);if(s){let t=s[1]||0,n=s[2]||0,r=s[3]||0;return{browser:e,version:`${t}.${n}.${r}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":e2,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},e8=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",e6=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",e5=()=>ew??(ew=e3());function e9(...e){let t=globalThis.ReadableStream;if(void 0===t)throw Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new t(...e)}function e7(e){let t=Symbol.asyncIterator in e?e[Symbol.asyncIterator]():e[Symbol.iterator]();return e9({start(){},async pull(e){let{done:s,value:n}=await t.next();s?e.close():e.enqueue(n)},async cancel(){await t.return?.()}})}function te(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function tt(e){if(null===e||"object"!=typeof e)return;if(e[Symbol.asyncIterator])return void await e[Symbol.asyncIterator]().return?.();let t=e.getReader(),s=t.cancel();t.releaseLock(),await s}let ts=({headers:e,body:t})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(t)}),tn="RFC3986",tr={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},ti=(Object.prototype.hasOwnProperty,Array.isArray),ta=(()=>{let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})();function to(e,t){if(ti(e)){let s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}let tl=Object.prototype.hasOwnProperty,tc={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},tu=Array.isArray,th=Array.prototype.push,td=function(e,t){th.apply(e,tu(t)?t:[t])},tf=Date.prototype.toISOString,tp={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,n,r)=>{if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===s)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let a="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,s=[];for(let e=0;e<t.length;++e){let n=t.charCodeAt(e);if(45===n||46===n||95===n||126===n||n>=48&&n<=57||n>=65&&n<=90||n>=97&&n<=122||"RFC1738"===r&&(40===n||41===n)){s[s.length]=t.charAt(e);continue}if(n<128){s[s.length]=ta[n];continue}if(n<2048){s[s.length]=ta[192|n>>6]+ta[128|63&n];continue}if(n<55296||n>=57344){s[s.length]=ta[224|n>>12]+ta[128|n>>6&63]+ta[128|63&n];continue}e+=1,n=65536+((1023&n)<<10|1023&t.charCodeAt(e)),s[s.length]=ta[240|n>>18]+ta[128|n>>12&63]+ta[128|n>>6&63]+ta[128|63&n]}a+=s.join("")}return a},encodeValuesOnly:!1,format:tn,formatter:tr[tn],indices:!1,serializeDate:e=>tf.call(e),skipNulls:!1,strictNullHandling:!1},tm={};function tg(e){let t;return(eb??(eb=(t=new globalThis.TextEncoder).encode.bind(t)))(e)}function ty(e){let t;return(ev??(ev=(t=new globalThis.TextDecoder).decode.bind(t)))(e)}class t_{constructor(){n.set(this,void 0),r.set(this,void 0),ex(this,n,new Uint8Array,"f"),ex(this,r,null,"f")}decode(e){let t;if(null==e)return[];let s=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?tg(e):e;ex(this,n,function(e){let t=0;for(let s of e)t+=s.length;let s=new Uint8Array(t),n=0;for(let t of e)s.set(t,n),n+=t.length;return s}([eS(this,n,"f"),s]),"f");let i=[];for(;null!=(t=function(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}(eS(this,n,"f"),eS(this,r,"f")));){if(t.carriage&&null==eS(this,r,"f")){ex(this,r,t.index,"f");continue}if(null!=eS(this,r,"f")&&(t.index!==eS(this,r,"f")+1||t.carriage)){i.push(ty(eS(this,n,"f").subarray(0,eS(this,r,"f")-1))),ex(this,n,eS(this,n,"f").subarray(eS(this,r,"f")),"f"),ex(this,r,null,"f");continue}let e=null!==eS(this,r,"f")?t.preceding-1:t.preceding,s=ty(eS(this,n,"f").subarray(0,e));i.push(s),ex(this,n,eS(this,n,"f").subarray(t.index),"f"),ex(this,r,null,"f")}return i}flush(){return eS(this,n,"f").length?this.decode("\n"):[]}}n=new WeakMap,r=new WeakMap,t_.NEWLINE_CHARS=new Set(["\n","\r"]),t_.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class tw{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;async function*n(){if(s)throw new eO("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let n=!1;try{for await(let s of tb(e,t))if(!n){if(s.data.startsWith("[DONE]")){n=!0;continue}if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if(t&&t.error)throw new eR(void 0,t.error,void 0,e.headers);yield t}else{let e;try{e=JSON.parse(s.data)}catch(e){throw console.error("Could not parse message into JSON:",s.data),console.error("From chunk:",s.raw),e}if("error"==s.event)throw new eR(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}}n=!0}catch(e){if(eA(e))return;throw e}finally{n||t.abort()}}return new tw(n,t)}static fromReadableStream(e,t){let s=!1;async function*n(){let t=new t_;for await(let s of te(e))for(let e of t.decode(s))yield e;for(let e of t.flush())yield e}return new tw(async function*(){if(s)throw new eO("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let e=!1;try{for await(let t of n())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(eA(e))return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){let n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new tw(()=>n(e),this.controller),new tw(()=>n(t),this.controller)]}toReadableStream(){let e,t=this;return e9({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:s,done:n}=await e.next();if(n)return t.close();let r=tg(JSON.stringify(s)+"\n");t.enqueue(r)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}async function*tb(e,t){if(!e.body){if(t.abort(),void 0!==globalThis.navigator&&"ReactNative"===globalThis.navigator.product)throw new eO("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api");throw new eO("Attempted to iterate over a response with no body")}let s=new tx,n=new t_;for await(let t of tv(te(e.body)))for(let e of n.decode(t)){let t=s.decode(e);t&&(yield t)}for(let e of n.flush()){let t=s.decode(e);t&&(yield t)}}async function*tv(e){let t=new Uint8Array;for await(let s of e){let e;if(null==s)continue;let n=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?tg(s):s,r=new Uint8Array(t.length+n.length);for(r.set(t),r.set(n,t.length),t=r;-1!==(e=function(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1]||13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return -1}(t));)yield t.slice(0,e),t=t.slice(e)}t.length>0&&(yield t)}class tx{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){let s=e.indexOf(":");return -1!==s?[e.substring(0,s),t,e.substring(s+t.length)]:[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}async function tS(e,t){let{response:s,requestLogID:n,retryOfRequestLogID:r,startTime:i}=t,a=await (async()=>{if(t.options.stream)return(e0(e).debug("response",s.status,s.url,s.headers,s.body),t.options.__streamClass)?t.options.__streamClass.fromSSEResponse(s,t.controller):tw.fromSSEResponse(s,t.controller);if(204===s.status)return null;if(t.options.__binaryResponse)return s;let n=s.headers.get("content-type"),r=n?.split(";")[0]?.trim();return r?.includes("application/json")||r?.endsWith("+json")?t$(await s.json(),s):await s.text()})();return e0(e).debug(`[${n}] response parsed`,e1({retryOfRequestLogID:r,url:s.url,status:s.status,body:a,durationMs:Date.now()-i})),a}function t$(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}class tA extends Promise{constructor(e,t,s=tS){super(e=>{e(null)}),this.responsePromise=t,this.parseResponse=s,i.set(this,void 0),ex(this,i,e,"f")}_thenUnwrap(e){return new tA(eS(this,i,"f"),this.responsePromise,async(t,s)=>t$(e(await this.parseResponse(t,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(eS(this,i,"f"),e))),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}i=new WeakMap;class tI{constructor(e,t,s,n){a.set(this,void 0),ex(this,a,e,"f"),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageRequestOptions()}async getNextPage(){let e=this.nextPageRequestOptions();if(!e)throw new eO("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await eS(this,a,"f").requestAPIList(this.constructor,e)}async *iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async *[(a=new WeakMap,Symbol.asyncIterator)](){for await(let e of this.iterPages())for(let t of e.getPaginatedItems())yield t}}class tO extends tA{constructor(e,t,s){super(e,t,async(e,t)=>new s(e,t.response,await tS(e,t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}class tR extends tI{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class tk extends tI{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageRequestOptions(){var e;let t=this.getPaginatedItems(),s=t[t.length-1]?.id;return s?{...this.options,query:{..."object"!=typeof(e=this.options.query)?{}:e??{},after:s}}:null}}let tE=()=>{if("undefined"==typeof File){let{process:e}=globalThis;throw Error("`File` is not defined as a global, which is required for file uploads."+("string"==typeof e?.versions?.node&&20>parseInt(e.versions.node.split("."))?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function tP(e,t,s){return tE(),new File(e,t??"unknown_file",s)}function tC(e){return("object"==typeof e&&null!==e&&("name"in e&&e.name&&String(e.name)||"url"in e&&e.url&&String(e.url)||"filename"in e&&e.filename&&String(e.filename)||"path"in e&&e.path&&String(e.path))||"").split(/[\\/]/).pop()||void 0}let tT=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tN=async(e,t)=>({...e,body:await tj(e.body,t)}),tM=new WeakMap,tj=async(e,t)=>{if(!await function(e){let t="function"==typeof e?e:e.fetch,s=tM.get(t);if(s)return s;let n=(async()=>{try{let e="Response"in t?t.Response:(await t("data:,")).constructor,s=new FormData;if(s.toString()===await new e(s).text())return!1;return!0}catch{return!0}})();return tM.set(t,n),n}(t))throw TypeError("The provided fetch function does not support file uploads with the current global FormData class.");let s=new FormData;return await Promise.all(Object.entries(e||{}).map(([e,t])=>tW(s,e,t))),s},tL=e=>e instanceof Blob&&"name"in e,tD=e=>"object"==typeof e&&null!==e&&(e instanceof Response||tT(e)||tL(e)),tB=e=>{if(tD(e))return!0;if(Array.isArray(e))return e.some(tB);if(e&&"object"==typeof e){for(let t in e)if(tB(e[t]))return!0}return!1},tW=async(e,t,s)=>{if(void 0!==s){if(null==s)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if(s instanceof Response)e.append(t,tP([await s.blob()],tC(s)));else if(tT(s))e.append(t,tP([await new Response(e7(s)).blob()],tC(s)));else if(tL(s))e.append(t,s,tC(s));else if(Array.isArray(s))await Promise.all(s.map(s=>tW(e,t+"[]",s)));else if("object"==typeof s)await Promise.all(Object.entries(s).map(([s,n])=>tW(e,`${t}[${s}]`,n)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`)}},tU=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tq=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&tU(e),tF=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob;async function tX(e,t,s){if(tE(),tq(e=await e))return e instanceof File?e:tP([await e.arrayBuffer()],e.name);if(tF(e)){let n=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()),tP(await tJ(n),t,s)}let n=await tJ(e);if(t||(t=tC(e)),!s?.type){let e=n.find(e=>"object"==typeof e&&"type"in e&&e.type);"string"==typeof e&&(s={...s,type:e})}return tP(n,t,s)}async function tJ(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(tU(e))t.push(e instanceof Blob?e:await e.arrayBuffer());else if(tT(e))for await(let s of e)t.push(...await tJ(s));else{let t=e?.constructor?.name;throw Error(`Unexpected data type: ${typeof e}${t?`; constructor: ${t}`:""}${function(e){if("object"!=typeof e||null===e)return"";let t=Object.getOwnPropertyNames(e);return`; props: [${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`)}return t}class tH{constructor(e){this._client=e}}function tK(e){return e.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}let tV=((e=tK)=>function(t,...s){let n;if(1===t.length)return t[0];let r=!1,i=t.reduce((t,n,i)=>(/[?#]/.test(n)&&(r=!0),t+n+(i===s.length?"":(r?encodeURIComponent:e)(String(s[i])))),""),a=i.split(/[?#]/,1)[0],o=[],l=/(?<=^|\/)(?:\.|%2e){1,2}(?=\/|$)/gi;for(;null!==(n=l.exec(a));)o.push({start:n.index,length:n[0].length});if(o.length>0){let e=0,t=o.reduce((t,s)=>{let n=" ".repeat(s.start-e),r="^".repeat(s.length);return e=s.start+s.length,t+n+r},"");throw new eO(`Path parameters result in path with invalid segments:
${i}
${t}`)}return i})(tK);class tz extends tH{list(e,t={},s){return this._client.getAPIList(tV`/chat/completions/${e}/messages`,tk,{query:t,...s})}}let tQ=e=>e?.role==="assistant",tG=e=>e?.role==="tool";class tY{constructor(){o.add(this),this.controller=new AbortController,l.set(this,void 0),c.set(this,()=>{}),u.set(this,()=>{}),h.set(this,void 0),d.set(this,()=>{}),f.set(this,()=>{}),p.set(this,{}),m.set(this,!1),g.set(this,!1),y.set(this,!1),_.set(this,!1),ex(this,l,new Promise((e,t)=>{ex(this,c,e,"f"),ex(this,u,t,"f")}),"f"),ex(this,h,new Promise((e,t)=>{ex(this,d,e,"f"),ex(this,f,t,"f")}),"f"),eS(this,l,"f").catch(()=>{}),eS(this,h,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},eS(this,o,"m",w).bind(this))},0)}_connected(){this.ended||(eS(this,c,"f").call(this),this._emit("connect"))}get ended(){return eS(this,m,"f")}get errored(){return eS(this,g,"f")}get aborted(){return eS(this,y,"f")}abort(){this.controller.abort()}on(e,t){return(eS(this,p,"f")[e]||(eS(this,p,"f")[e]=[])).push({listener:t}),this}off(e,t){let s=eS(this,p,"f")[e];if(!s)return this;let n=s.findIndex(e=>e.listener===t);return n>=0&&s.splice(n,1),this}once(e,t){return(eS(this,p,"f")[e]||(eS(this,p,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,s)=>{ex(this,_,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)})}async done(){ex(this,_,!0,"f"),await eS(this,h,"f")}_emit(e,...t){if(eS(this,m,"f"))return;"end"===e&&(ex(this,m,!0,"f"),eS(this,d,"f").call(this));let s=eS(this,p,"f")[e];if(s&&(eS(this,p,"f")[e]=s.filter(e=>!e.once),s.forEach(({listener:e})=>e(...t))),"abort"===e){let e=t[0];eS(this,_,"f")||s?.length||Promise.reject(e),eS(this,u,"f").call(this,e),eS(this,f,"f").call(this,e),this._emit("end");return}if("error"===e){let e=t[0];eS(this,_,"f")||s?.length||Promise.reject(e),eS(this,u,"f").call(this,e),eS(this,f,"f").call(this,e),this._emit("end")}}_emitFinal(){}}function tZ(e){return e?.$brand==="auto-parseable-response-format"}function t0(e){return e?.$brand==="auto-parseable-tool"}function t1(e,t){let s=e.choices.map(e=>{var s,n;if("length"===e.finish_reason)throw new eW;if("content_filter"===e.finish_reason)throw new eU;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:e.message.tool_calls?.map(e=>(function(e,t){let s=e.tools?.find(e=>e.function?.name===t.function.name);return{...t,function:{...t.function,parsed_arguments:t0(s)?s.$parseRaw(t.function.arguments):s?.function.strict?JSON.parse(t.function.arguments):null}}})(t,e))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?(s=t,n=e.message.content,s.response_format?.type!=="json_schema"?null:s.response_format?.type==="json_schema"?"$parseRaw"in s.response_format?s.response_format.$parseRaw(n):JSON.parse(n):null):null}}});return{...e,choices:s}}function t2(e){return!!tZ(e.response_format)||(e.tools?.some(e=>t0(e)||"function"===e.type&&!0===e.function.strict)??!1)}l=new WeakMap,c=new WeakMap,u=new WeakMap,h=new WeakMap,d=new WeakMap,f=new WeakMap,p=new WeakMap,m=new WeakMap,g=new WeakMap,y=new WeakMap,_=new WeakMap,o=new WeakSet,w=function(e){if(ex(this,g,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new ek),e instanceof ek)return ex(this,y,!0,"f"),this._emit("abort",e);if(e instanceof eO)return this._emit("error",e);if(e instanceof Error){let t=new eO(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new eO(String(e)))};class t4 extends tY{constructor(){super(...arguments),b.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){this._chatCompletions.push(e),this._emit("chatCompletion",e);let t=e.choices[0]?.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),tG(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(tQ(e)&&e.tool_calls)for(let t of e.tool_calls)"function"===t.type&&this._emit("functionToolCall",t.function)}}async finalChatCompletion(){await this.done();let e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new eO("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),eS(this,b,"m",v).call(this)}async finalMessage(){return await this.done(),eS(this,b,"m",x).call(this)}async finalFunctionToolCall(){return await this.done(),eS(this,b,"m",S).call(this)}async finalFunctionToolCallResult(){return await this.done(),eS(this,b,"m",$).call(this)}async totalUsage(){return await this.done(),eS(this,b,"m",A).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){let e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);let t=eS(this,b,"m",x).call(this);t&&this._emit("finalMessage",t);let s=eS(this,b,"m",v).call(this);s&&this._emit("finalContent",s);let n=eS(this,b,"m",S).call(this);n&&this._emit("finalFunctionToolCall",n);let r=eS(this,b,"m",$).call(this);null!=r&&this._emit("finalFunctionToolCallResult",r),this._chatCompletions.some(e=>e.usage)&&this._emit("totalUsage",eS(this,b,"m",A).call(this))}async _createChatCompletion(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eS(this,b,"m",I).call(this,t);let r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(t1(r,t))}async _runChatCompletion(e,t,s){for(let e of t.messages)this._addMessage(e,!1);return await this._createChatCompletion(e,t,s)}async _runTools(e,t,s){let n="tool",{tool_choice:r="auto",stream:i,...a}=t,o="string"!=typeof r&&r?.function?.name,{maxChatCompletions:l=10}=s||{},c=t.tools.map(e=>{if(t0(e)){if(!e.$callback)throw new eO("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e}),u={};for(let e of c)"function"===e.type&&(u[e.function.name||e.function.function.name]=e.function);let h="tools"in t?c.map(e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e):void 0;for(let e of t.messages)this._addMessage(e,!1);for(let t=0;t<l;++t){let t=await this._createChatCompletion(e,{...a,tool_choice:r,tools:h,messages:[...this.messages]},s),i=t.choices[0]?.message;if(!i)throw new eO("missing message in ChatCompletion response");if(!i.tool_calls?.length)break;for(let e of i.tool_calls){let t;if("function"!==e.type)continue;let s=e.id,{name:r,arguments:i}=e.function,a=u[r];if(a){if(o&&o!==r){let e=`Invalid tool_call: ${JSON.stringify(r)}. ${JSON.stringify(o)} requested. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}}else{let e=`Invalid tool_call: ${JSON.stringify(r)}. Available options are: ${Object.keys(u).map(e=>JSON.stringify(e)).join(", ")}. Please try again`;this._addMessage({role:n,tool_call_id:s,content:e});continue}try{t="function"==typeof a.parse?await a.parse(i):i}catch(t){let e=t instanceof Error?t.message:String(t);this._addMessage({role:n,tool_call_id:s,content:e});continue}let l=await a.function(t,this),c=eS(this,b,"m",O).call(this,l);if(this._addMessage({role:n,tool_call_id:s,content:c}),o)return}}}}b=new WeakSet,v=function(){return eS(this,b,"m",x).call(this).content??null},x=function(){let e=this.messages.length;for(;e-- >0;){let t=this.messages[e];if(tQ(t))return{...t,content:t.content??null,refusal:t.refusal??null}}throw new eO("stream ended without producing a ChatCompletionMessage with role=assistant")},S=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(tQ(t)&&t?.tool_calls?.length)return t.tool_calls.at(-1)?.function}},$=function(){for(let e=this.messages.length-1;e>=0;e--){let t=this.messages[e];if(tG(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some(e=>"assistant"===e.role&&e.tool_calls?.some(e=>"function"===e.type&&e.id===t.tool_call_id)))return t.content}},A=function(){let e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(let{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},I=function(e){if(null!=e.n&&e.n>1)throw new eO("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},O=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class t3 extends t4{static runTools(e,t,s){let n=new t3,r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}_addMessage(e,t=!0){super._addMessage(e,t),tQ(e)&&e.content&&this._emit("content",e.content)}}let t8={STR:1,NUM:2,ARR:4,OBJ:8,NULL:16,BOOL:32,NAN:64,INFINITY:128,MINUS_INFINITY:256,ALL:511};class t6 extends Error{}class t5 extends Error{}let t9=(e,t)=>{let s=e.length,n=0,r=e=>{throw new t6(`${e} at position ${n}`)},i=e=>{throw new t5(`${e} at position ${n}`)},a=()=>(h(),n>=s&&r("Unexpected end of input"),'"'===e[n])?o():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||t8.NULL&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||t8.BOOL&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||t8.BOOL&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||t8.INFINITY&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||t8.MINUS_INFINITY&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||t8.NAN&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u(),o=()=>{let a=n,o=!1;for(n++;n<s&&('"'!==e[n]||o&&"\\"===e[n-1]);)o="\\"===e[n]&&!o,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(a,++n-Number(o)))}catch(e){i(String(e))}else if(t8.STR&t)try{return JSON.parse(e.substring(a,n-Number(o))+'"')}catch(t){return JSON.parse(e.substring(a,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,h();let i={};try{for(;"}"!==e[n];){if(h(),n>=s&&t8.OBJ&t)return i;let r=o();h(),n++;try{let e=a();Object.defineProperty(i,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(e){if(t8.OBJ&t)return i;throw e}h(),","===e[n]&&n++}}catch(e){if(t8.OBJ&t)return i;r("Expected '}' at end of object")}return n++,i},c=()=>{n++;let s=[];try{for(;"]"!==e[n];)s.push(a()),h(),","===e[n]&&n++}catch(e){if(t8.ARR&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&t8.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(s){if(t8.NUM&t)try{if("."===e[e.length-1])return JSON.parse(e.substring(0,e.lastIndexOf(".")));return JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(e){}i(String(s))}}let a=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||t8.NUM&t||r("Unterminated number literal");try{return JSON.parse(e.substring(a,n))}catch(s){"-"===e.substring(a,n)&&t8.NUM&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(a,e.lastIndexOf("e")))}catch(e){i(String(e))}}},h=()=>{for(;n<s&&" \n\r	".includes(e[n]);)n++};return a()},t7=e=>(function(e,t=t8.ALL){if("string"!=typeof e)throw TypeError(`expecting str, got ${typeof e}`);if(!e.trim())throw Error(`${e} is empty`);return t9(e.trim(),t)})(e,t8.ALL^t8.NUM);class se extends t4{constructor(e){super(),R.add(this),k.set(this,void 0),E.set(this,void 0),P.set(this,void 0),ex(this,k,e,"f"),ex(this,E,[],"f")}get currentChatCompletionSnapshot(){return eS(this,P,"f")}static fromReadableStream(e){let t=new se(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,s){let n=new se(t);return n._run(()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createChatCompletion(e,t,s){super._createChatCompletion;let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eS(this,R,"m",C).call(this);let r=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});for await(let e of(this._connected(),r))eS(this,R,"m",N).call(this,e);if(r.controller.signal?.aborted)throw new ek;return this._addChatCompletion(eS(this,R,"m",L).call(this))}async _fromReadableStream(e,t){let s,n=t?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),eS(this,R,"m",C).call(this),this._connected();let r=tw.fromReadableStream(e,this.controller);for await(let e of r)s&&s!==e.id&&this._addChatCompletion(eS(this,R,"m",L).call(this)),eS(this,R,"m",N).call(this,e),s=e.id;if(r.controller.signal?.aborted)throw new ek;return this._addChatCompletion(eS(this,R,"m",L).call(this))}[(k=new WeakMap,E=new WeakMap,P=new WeakMap,R=new WeakSet,C=function(){this.ended||ex(this,P,void 0,"f")},T=function(e){let t=eS(this,E,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},eS(this,E,"f")[e.index]=t),t},N=function(e){if(this.ended)return;let t=eS(this,R,"m",B).call(this,e);for(let s of(this._emit("chunk",e,t),e.choices)){let e=t.choices[s.index];null!=s.delta.content&&e.message?.role==="assistant"&&e.message?.content&&(this._emit("content",s.delta.content,e.message.content),this._emit("content.delta",{delta:s.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=s.delta.refusal&&e.message?.role==="assistant"&&e.message?.refusal&&this._emit("refusal.delta",{delta:s.delta.refusal,snapshot:e.message.refusal}),s.logprobs?.content!=null&&e.message?.role==="assistant"&&this._emit("logprobs.content.delta",{content:s.logprobs?.content,snapshot:e.logprobs?.content??[]}),s.logprobs?.refusal!=null&&e.message?.role==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:s.logprobs?.refusal,snapshot:e.logprobs?.refusal??[]});let n=eS(this,R,"m",T).call(this,e);for(let t of(e.finish_reason&&(eS(this,R,"m",j).call(this,e),null!=n.current_tool_call_index&&eS(this,R,"m",M).call(this,e,n.current_tool_call_index)),s.delta.tool_calls??[]))n.current_tool_call_index!==t.index&&(eS(this,R,"m",j).call(this,e),null!=n.current_tool_call_index&&eS(this,R,"m",M).call(this,e,n.current_tool_call_index)),n.current_tool_call_index=t.index;for(let t of s.delta.tool_calls??[]){let s=e.message.tool_calls?.[t.index];s?.type&&(s?.type==="function"?this._emit("tool_calls.function.arguments.delta",{name:s.function?.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:t.function?.arguments??""}):s?.type)}}},M=function(e,t){if(eS(this,R,"m",T).call(this,e).done_tool_calls.has(t))return;let s=e.message.tool_calls?.[t];if(!s)throw Error("no tool call snapshot");if(!s.type)throw Error("tool call snapshot missing `type`");if("function"===s.type){let e=eS(this,k,"f")?.tools?.find(e=>"function"===e.type&&e.function.name===s.function.name);this._emit("tool_calls.function.arguments.done",{name:s.function.name,index:t,arguments:s.function.arguments,parsed_arguments:t0(e)?e.$parseRaw(s.function.arguments):e?.function.strict?JSON.parse(s.function.arguments):null})}else s.type},j=function(e){let t=eS(this,R,"m",T).call(this,e);if(e.message.content&&!t.content_done){t.content_done=!0;let s=eS(this,R,"m",D).call(this);this._emit("content.done",{content:e.message.content,parsed:s?s.$parseRaw(e.message.content):null})}e.message.refusal&&!t.refusal_done&&(t.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),e.logprobs?.content&&!t.logprobs_content_done&&(t.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),e.logprobs?.refusal&&!t.logprobs_refusal_done&&(t.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},L=function(){if(this.ended)throw new eO("stream has ended, this shouldn't happen");let e=eS(this,P,"f");if(!e)throw new eO("request ended without sending any chunks");return ex(this,P,void 0,"f"),ex(this,E,[],"f"),function(e,t){var s;let{id:n,choices:r,created:i,model:a,system_fingerprint:o,...l}=e;return s={...l,id:n,choices:r.map(({message:t,finish_reason:s,index:n,logprobs:r,...i})=>{if(!s)throw new eO(`missing finish_reason for choice ${n}`);let{content:a=null,function_call:o,tool_calls:l,...c}=t,u=t.role;if(!u)throw new eO(`missing role for choice ${n}`);if(o){let{arguments:e,name:l}=o;if(null==e)throw new eO(`missing function_call.arguments for choice ${n}`);if(!l)throw new eO(`missing function_call.name for choice ${n}`);return{...i,message:{content:a,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...i,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:a,refusal:t.refusal??null,tool_calls:l.map((t,s)=>{let{function:r,type:i,id:a,...o}=t,{arguments:l,name:c,...u}=r||{};if(null==a)throw new eO(`missing choices[${n}].tool_calls[${s}].id
${st(e)}`);if(null==i)throw new eO(`missing choices[${n}].tool_calls[${s}].type
${st(e)}`);if(null==c)throw new eO(`missing choices[${n}].tool_calls[${s}].function.name
${st(e)}`);if(null==l)throw new eO(`missing choices[${n}].tool_calls[${s}].function.arguments
${st(e)}`);return{...o,id:a,type:i,function:{...u,name:c,arguments:l}}})}}:{...i,message:{...c,content:a,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}),created:i,model:a,object:"chat.completion",...o?{system_fingerprint:o}:{}},t&&t2(t)?t1(s,t):{...s,choices:s.choices.map(e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}}))}}(e,eS(this,k,"f"))},D=function(){let e=eS(this,k,"f")?.response_format;return tZ(e)?e:null},B=function(e){var t,s,n,r;let i=eS(this,P,"f"),{choices:a,...o}=e;for(let{delta:a,finish_reason:l,index:c,logprobs:u=null,...h}of(i?Object.assign(i,o):i=ex(this,P,{...o,choices:[]},"f"),e.choices)){let e=i.choices[c];if(e||(e=i.choices[c]={finish_reason:l,index:c,message:{},logprobs:u,...h}),u)if(e.logprobs){let{content:n,refusal:r,...i}=u;Object.assign(e.logprobs,i),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},u);if(l&&(e.finish_reason=l,eS(this,k,"f")&&t2(eS(this,k,"f")))){if("length"===l)throw new eW;if("content_filter"===l)throw new eU}if(Object.assign(e,h),!a)continue;let{content:o,refusal:d,function_call:f,role:p,tool_calls:m,...g}=a;if(Object.assign(e.message,g),d&&(e.message.refusal=(e.message.refusal||"")+d),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),o&&(e.message.content=(e.message.content||"")+o,!e.message.refusal&&eS(this,R,"m",D).call(this)&&(e.message.parsed=t7(e.message.content))),m)for(let{index:t,id:s,type:n,function:i,...a}of(e.message.tool_calls||(e.message.tool_calls=[]),m)){let o=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(o,a),s&&(o.id=s),n&&(o.type=n),i&&(o.function??(o.function={name:i.name??"",arguments:""})),i?.name&&(o.function.name=i.name),i?.arguments&&(o.function.arguments+=i.arguments,function(e,t){if(!e)return!1;let s=e.tools?.find(e=>e.function?.name===t.function.name);return t0(s)||s?.function.strict||!1}(eS(this,k,"f"),o)&&(o.function.parsed_arguments=t7(o.function.arguments)))}}return i},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("chunk",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new tw(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function st(e){return JSON.stringify(e)}class ss extends se{static fromReadableStream(e){let t=new ss(null);return t._run(()=>t._fromReadableStream(e)),t}static runTools(e,t,s){let n=new ss(t),r={...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run(()=>n._runTools(e,t,r)),n}}class sn extends tH{constructor(){super(...arguments),this.messages=new tz(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(tV`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(tV`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/chat/completions",tk,{query:e,...t})}delete(e,t){return this._client.delete(tV`/chat/completions/${e}`,t)}parse(e,t){for(let t of e.tools??[]){if("function"!==t.type)throw new eO(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new eO(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}return this._client.chat.completions.create(e,{...t,headers:{...t?.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(t=>t1(t,e))}runTools(e,t){return e.stream?ss.runTools(this._client,e,t):t3.runTools(this._client,e,t)}stream(e,t){return se.createChatCompletion(this._client,e,t)}}sn.Messages=tz;class sr extends tH{constructor(){super(...arguments),this.completions=new sn(this._client)}}sr.Completions=sn;let si=Symbol("brand.privateNullableHeaders"),sa=Array.isArray,so=e=>{let t=new Headers,s=new Set;for(let n of e){let e=new Set;for(let[r,i]of function*(e){let t;if(!e)return;if(si in e){let{values:t,nulls:s}=e;for(let e of(yield*t.entries(),s))yield[e,null];return}let s=!1;for(let n of(e instanceof Headers?t=e.entries():sa(e)?t=e:(s=!0,t=Object.entries(e??{})),t)){let e=n[0];if("string"!=typeof e)throw TypeError("expected header name to be a string");let t=sa(n[1])?n[1]:[n[1]],r=!1;for(let n of t)void 0!==n&&(s&&!r&&(r=!0,yield[e,null]),yield[e,n])}}(n)){let n=r.toLowerCase();e.has(n)||(t.delete(r),e.add(n)),null===i?(t.delete(r),s.add(n)):(t.append(r,i),s.delete(n))}}return{[si]:!0,values:t,nulls:s}};class sl extends tH{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:so([{Accept:"application/octet-stream"},t?.headers]),__binaryResponse:!0})}}class sc extends tH{create(e,t){return this._client.post("/audio/transcriptions",tN({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class su extends tH{create(e,t){return this._client.post("/audio/translations",tN({body:e,...t,__metadata:{model:e.model}},this._client))}}class sh extends tH{constructor(){super(...arguments),this.transcriptions=new sc(this._client),this.translations=new su(this._client),this.speech=new sl(this._client)}}sh.Transcriptions=sc,sh.Translations=su,sh.Speech=sl;class sd extends tH{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(tV`/batches/${e}`,t)}list(e={},t){return this._client.getAPIList("/batches",tk,{query:e,...t})}cancel(e,t){return this._client.post(tV`/batches/${e}/cancel`,t)}}class sf extends tH{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(tV`/assistants/${e}`,{...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(tV`/assistants/${e}`,{body:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/assistants",tk,{query:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(tV`/assistants/${e}`,{...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sp extends tH{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sm extends tH{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}}class sg extends tH{constructor(){super(...arguments),this.sessions=new sp(this._client),this.transcriptionSessions=new sm(this._client)}}sg.Sessions=sp,sg.TranscriptionSessions=sm;class sy extends tH{create(e,t,s){return this._client.post(tV`/threads/${e}/messages`,{body:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(tV`/threads/${n}/messages/${e}`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(tV`/threads/${n}/messages/${e}`,{body:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(tV`/threads/${e}/messages`,tk,{query:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{thread_id:n}=t;return this._client.delete(tV`/threads/${n}/messages/${e}`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s_ extends tH{retrieve(e,t,s){let{thread_id:n,run_id:r,...i}=t;return this._client.get(tV`/threads/${n}/runs/${r}/steps/${e}`,{query:i,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t,s){let{thread_id:n,...r}=t;return this._client.getAPIList(tV`/threads/${n}/runs/${e}/steps`,tk,{query:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}let sw=e=>{if("undefined"!=typeof Buffer){let t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{let t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}},sb=e=>void 0!==globalThis.process?globalThis.process.env?.[e]?.trim()??void 0:void 0!==globalThis.Deno?globalThis.Deno.env?.get?.(e)?.trim():void 0;class sv extends tY{constructor(){super(...arguments),W.add(this),q.set(this,[]),F.set(this,{}),X.set(this,{}),J.set(this,void 0),H.set(this,void 0),K.set(this,void 0),V.set(this,void 0),z.set(this,void 0),Q.set(this,void 0),G.set(this,void 0),Y.set(this,void 0),Z.set(this,void 0)}[(q=new WeakMap,F=new WeakMap,X=new WeakMap,J=new WeakMap,H=new WeakMap,K=new WeakMap,V=new WeakMap,z=new WeakMap,Q=new WeakMap,G=new WeakMap,Y=new WeakMap,Z=new WeakMap,W=new WeakSet,Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){let t=new U;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){let s=t?.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();let n=tw.fromReadableStream(e,this.controller);for await(let e of n)eS(this,W,"m",ee).call(this,e);if(n.controller.signal?.aborted)throw new ek;return this._addRun(eS(this,W,"m",et).call(this))}toReadableStream(){return new tw(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n){let r=new U;return r._run(()=>r._runToolAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}async _createToolAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.submitToolOutputs(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eS(this,W,"m",ee).call(this,e);if(a.controller.signal?.aborted)throw new ek;return this._addRun(eS(this,W,"m",et).call(this))}static createThreadAssistantStream(e,t,s){let n=new U;return n._run(()=>n._threadAssistantStream(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}static createAssistantStream(e,t,s,n){let r=new U;return r._run(()=>r._runAssistantStream(e,t,s,{...n,headers:{...n?.headers,"X-Stainless-Helper-Method":"stream"}})),r}currentEvent(){return eS(this,G,"f")}currentRun(){return eS(this,Y,"f")}currentMessageSnapshot(){return eS(this,J,"f")}currentRunStepSnapshot(){return eS(this,Z,"f")}async finalRunSteps(){return await this.done(),Object.values(eS(this,F,"f"))}async finalMessages(){return await this.done(),Object.values(eS(this,X,"f"))}async finalRun(){if(await this.done(),!eS(this,H,"f"))throw Error("Final run was not received.");return eS(this,H,"f")}async _createThreadAssistantStream(e,t,s){let n=s?.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort()));let r={...t,stream:!0},i=await e.createAndRun(r,{...s,signal:this.controller.signal});for await(let e of(this._connected(),i))eS(this,W,"m",ee).call(this,e);if(i.controller.signal?.aborted)throw new ek;return this._addRun(eS(this,W,"m",et).call(this))}async _createAssistantStream(e,t,s,n){let r=n?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort()));let i={...s,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});for await(let e of(this._connected(),a))eS(this,W,"m",ee).call(this,e);if(a.controller.signal?.aborted)throw new ek;return this._addRun(eS(this,W,"m",et).call(this))}static accumulateDelta(e,t){for(let[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null==t||"index"===s||"type"===s){e[s]=n;continue}if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else if(eX(t)&&eX(n))t=this.accumulateDelta(t,n);else if(Array.isArray(t)&&Array.isArray(n)){if(t.every(e=>"string"==typeof e||"number"==typeof e)){t.push(...n);continue}for(let e of n){if(!eX(e))throw Error(`Expected array delta entry to be an object but got: ${e}`);let s=e.index;if(null==s)throw console.error(e),Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);let n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`);e[s]=t}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n){return await this._createToolAssistantStream(t,e,s,n)}}U=sv,ee=function(e){if(!this.ended)switch(ex(this,G,e,"f"),eS(this,W,"m",er).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":eS(this,W,"m",el).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":eS(this,W,"m",en).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":eS(this,W,"m",es).call(this,e);break;case"error":throw Error("Encountered an error event in event processing - errors should be processed earlier")}},et=function(){if(this.ended)throw new eO("stream has ended, this shouldn't happen");if(!eS(this,H,"f"))throw Error("Final run has not been received");return eS(this,H,"f")},es=function(e){let[t,s]=eS(this,W,"m",ea).call(this,e,eS(this,J,"f"));for(let e of(ex(this,J,t,"f"),eS(this,X,"f")[t.id]=t,s)){let s=t.content[e.index];s?.type=="text"&&this._emit("textCreated",s.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(let s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(n&&"text"==n.type)this._emit("textDelta",e,n.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=eS(this,K,"f")){if(eS(this,V,"f"))switch(eS(this,V,"f").type){case"text":this._emit("textDone",eS(this,V,"f").text,eS(this,J,"f"));break;case"image_file":this._emit("imageFileDone",eS(this,V,"f").image_file,eS(this,J,"f"))}ex(this,K,s.index,"f")}ex(this,V,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==eS(this,K,"f")){let t=e.data.content[eS(this,K,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,eS(this,J,"f"));break;case"text":this._emit("textDone",t.text,eS(this,J,"f"))}}eS(this,J,"f")&&this._emit("messageDone",e.data),ex(this,J,void 0,"f")}},en=function(e){let t=eS(this,W,"m",ei).call(this,e);switch(ex(this,Z,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":let s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(let e of s.step_details.tool_calls)e.index==eS(this,z,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(eS(this,Q,"f")&&this._emit("toolCallDone",eS(this,Q,"f")),ex(this,z,e.index,"f"),ex(this,Q,t.step_details.tool_calls[e.index],"f"),eS(this,Q,"f")&&this._emit("toolCallCreated",eS(this,Q,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":ex(this,Z,void 0,"f"),"tool_calls"==e.data.step_details.type&&eS(this,Q,"f")&&(this._emit("toolCallDone",eS(this,Q,"f")),ex(this,Q,void 0,"f")),this._emit("runStepDone",e.data,t)}},er=function(e){eS(this,q,"f").push(e),this._emit("event",e)},ei=function(e){switch(e.event){case"thread.run.step.created":return eS(this,F,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=eS(this,F,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){let n=U.accumulateDelta(t,s.delta);eS(this,F,"f")[e.data.id]=n}return eS(this,F,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":eS(this,F,"f")[e.data.id]=e.data}if(eS(this,F,"f")[e.data.id])return eS(this,F,"f")[e.data.id];throw Error("No snapshot available")},ea=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(let e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=eS(this,W,"m",eo).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},eo=function(e,t){return U.accumulateDelta(t,e)},el=function(e){switch(ex(this,Y,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":ex(this,H,e.data,"f"),eS(this,Q,"f")&&(this._emit("toolCallDone",eS(this,Q,"f")),ex(this,Q,void 0,"f"))}};class sx extends tH{constructor(){super(...arguments),this.steps=new s_(this._client)}create(e,t,s){let{include:n,...r}=t;return this._client.post(tV`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}retrieve(e,t,s){let{thread_id:n}=t;return this._client.get(tV`/threads/${n}/runs/${e}`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{thread_id:n,...r}=t;return this._client.post(tV`/threads/${n}/runs/${e}`,{body:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(tV`/threads/${e}/runs`,tk,{query:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{thread_id:n}=t;return this._client.post(tV`/threads/${n}/runs/${e}/cancel`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(n.id,{thread_id:e},s)}createAndStream(e,t,s){return sv.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){let n=so([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(e,t,{...s,headers:{...s?.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await eK(a);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return sv.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s){let{thread_id:n,...r}=t;return this._client.post(tV`/threads/${n}/runs/${e}/submit_tool_outputs`,{body:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers]),stream:t.stream??!1})}async submitToolOutputsAndPoll(e,t,s){let n=await this.submitToolOutputs(e,t,s);return await this.poll(n.id,t,s)}submitToolOutputsStream(e,t,s){return sv.createToolAssistantStream(e,this._client.beta.threads.runs,t,s)}}sx.Steps=s_;class sS extends tH{constructor(){super(...arguments),this.runs=new sx(this._client),this.messages=new sy(this._client)}create(e={},t){return this._client.post("/threads",{body:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(tV`/threads/${e}`,{...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(tV`/threads/${e}`,{body:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t){return this._client.delete(tV`/threads/${e}`,{...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers]),stream:e.stream??!1})}async createAndRunPoll(e,t){let s=await this.createAndRun(e,t);return await this.runs.poll(s.id,{thread_id:s.thread_id},t)}createAndRunStream(e,t){return sv.createThreadAssistantStream(e,this._client.beta.threads,t)}}sS.Runs=sx,sS.Messages=sy;class s$ extends tH{constructor(){super(...arguments),this.realtime=new sg(this._client),this.assistants=new sf(this._client),this.threads=new sS(this._client)}}s$.Realtime=sg,s$.Assistants=sf,s$.Threads=sS;class sA extends tH{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sI extends tH{retrieve(e,t,s){let{container_id:n}=t;return this._client.get(tV`/containers/${n}/files/${e}/content`,{...s,headers:so([{Accept:"application/binary"},s?.headers]),__binaryResponse:!0})}}class sO extends tH{constructor(){super(...arguments),this.content=new sI(this._client)}create(e,t,s){return this._client.post(tV`/containers/${e}/files`,tN({body:t,...s},this._client))}retrieve(e,t,s){let{container_id:n}=t;return this._client.get(tV`/containers/${n}/files/${e}`,s)}list(e,t={},s){return this._client.getAPIList(tV`/containers/${e}/files`,tk,{query:t,...s})}delete(e,t,s){let{container_id:n}=t;return this._client.delete(tV`/containers/${n}/files/${e}`,{...s,headers:so([{Accept:"*/*"},s?.headers])})}}sO.Content=sI;class sR extends tH{constructor(){super(...arguments),this.files=new sO(this._client)}create(e,t){return this._client.post("/containers",{body:e,...t})}retrieve(e,t){return this._client.get(tV`/containers/${e}`,t)}list(e={},t){return this._client.getAPIList("/containers",tk,{query:e,...t})}delete(e,t){return this._client.delete(tV`/containers/${e}`,{...t,headers:so([{Accept:"*/*"},t?.headers])})}}sR.Files=sO;class sk extends tH{create(e,t){let s=!!e.encoding_format,n=s?e.encoding_format:"base64";s&&e0(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);let r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(e0(this._client).debug("embeddings/decoding base64 embeddings from base64"),r._thenUnwrap(e=>(e&&e.data&&e.data.forEach(e=>{let t=e.embedding;e.embedding=sw(t)}),e)))}}class sE extends tH{retrieve(e,t,s){let{eval_id:n,run_id:r}=t;return this._client.get(tV`/evals/${n}/runs/${r}/output_items/${e}`,s)}list(e,t,s){let{eval_id:n,...r}=t;return this._client.getAPIList(tV`/evals/${n}/runs/${e}/output_items`,tk,{query:r,...s})}}class sP extends tH{constructor(){super(...arguments),this.outputItems=new sE(this._client)}create(e,t,s){return this._client.post(tV`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){let{eval_id:n}=t;return this._client.get(tV`/evals/${n}/runs/${e}`,s)}list(e,t={},s){return this._client.getAPIList(tV`/evals/${e}/runs`,tk,{query:t,...s})}delete(e,t,s){let{eval_id:n}=t;return this._client.delete(tV`/evals/${n}/runs/${e}`,s)}cancel(e,t,s){let{eval_id:n}=t;return this._client.post(tV`/evals/${n}/runs/${e}`,s)}}sP.OutputItems=sE;class sC extends tH{constructor(){super(...arguments),this.runs=new sP(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(tV`/evals/${e}`,t)}update(e,t,s){return this._client.post(tV`/evals/${e}`,{body:t,...s})}list(e={},t){return this._client.getAPIList("/evals",tk,{query:e,...t})}delete(e,t){return this._client.delete(tV`/evals/${e}`,t)}}sC.Runs=sP;class sT extends tH{create(e,t){return this._client.post("/files",tN({body:e,...t},this._client))}retrieve(e,t){return this._client.get(tV`/files/${e}`,t)}list(e={},t){return this._client.getAPIList("/files",tk,{query:e,...t})}delete(e,t){return this._client.delete(tV`/files/${e}`,t)}content(e,t){return this._client.get(tV`/files/${e}/content`,{...t,headers:so([{Accept:"application/binary"},t?.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){let n=new Set(["processed","error","deleted"]),r=Date.now(),i=await this.retrieve(e);for(;!i.status||!n.has(i.status);)if(await eK(t),i=await this.retrieve(e),Date.now()-r>s)throw new eP({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return i}}class sN extends tH{}class sM extends tH{run(e,t){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...t})}validate(e,t){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...t})}}class sj extends tH{constructor(){super(...arguments),this.graders=new sM(this._client)}}sj.Graders=sM;class sL extends tH{create(e,t,s){return this._client.getAPIList(tV`/fine_tuning/checkpoints/${e}/permissions`,tR,{body:t,method:"post",...s})}retrieve(e,t={},s){return this._client.get(tV`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}delete(e,t,s){let{fine_tuned_model_checkpoint:n}=t;return this._client.delete(tV`/fine_tuning/checkpoints/${n}/permissions/${e}`,s)}}class sD extends tH{constructor(){super(...arguments),this.permissions=new sL(this._client)}}sD.Permissions=sL;class sB extends tH{list(e,t={},s){return this._client.getAPIList(tV`/fine_tuning/jobs/${e}/checkpoints`,tk,{query:t,...s})}}class sW extends tH{constructor(){super(...arguments),this.checkpoints=new sB(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(tV`/fine_tuning/jobs/${e}`,t)}list(e={},t){return this._client.getAPIList("/fine_tuning/jobs",tk,{query:e,...t})}cancel(e,t){return this._client.post(tV`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return this._client.getAPIList(tV`/fine_tuning/jobs/${e}/events`,tk,{query:t,...s})}pause(e,t){return this._client.post(tV`/fine_tuning/jobs/${e}/pause`,t)}resume(e,t){return this._client.post(tV`/fine_tuning/jobs/${e}/resume`,t)}}sW.Checkpoints=sB;class sU extends tH{constructor(){super(...arguments),this.methods=new sN(this._client),this.jobs=new sW(this._client),this.checkpoints=new sD(this._client),this.alpha=new sj(this._client)}}sU.Methods=sN,sU.Jobs=sW,sU.Checkpoints=sD,sU.Alpha=sj;class sq extends tH{}class sF extends tH{constructor(){super(...arguments),this.graderModels=new sq(this._client)}}sF.GraderModels=sq;class sX extends tH{createVariation(e,t){return this._client.post("/images/variations",tN({body:e,...t},this._client))}edit(e,t){return this._client.post("/images/edits",tN({body:e,...t},this._client))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class sJ extends tH{retrieve(e,t){return this._client.get(tV`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",tR,e)}delete(e,t){return this._client.delete(tV`/models/${e}`,t)}}class sH extends tH{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function sK(e,t){let s=e.output.map(e=>{if("function_call"===e.type)return{...e,parsed_arguments:function(e,t){let s=function(e,t){return e.find(e=>"function"===e.type&&e.name===t)}(e.tools??[],t.name);return{...t,...t,parsed_arguments:function(e){return e?.$brand==="auto-parseable-tool"}(s)?s.$parseRaw(t.arguments):s?.strict?JSON.parse(t.arguments):null}}(t,e)};if("message"===e.type){let s=e.content.map(e=>{var s,n;return"output_text"===e.type?{...e,parsed:(s=t,n=e.text,s.text?.format?.type!=="json_schema"?null:"$parseRaw"in s.text?.format?(s.text?.format).$parseRaw(n):JSON.parse(n))}:e});return{...e,content:s}}return e}),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||sV(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(let e of n.output)if("message"===e.type){for(let t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed}return null}}),n}function sV(e){let t=[];for(let s of e.output)if("message"===s.type)for(let e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class sz extends tY{constructor(e){super(),ec.add(this),eu.set(this,void 0),eh.set(this,void 0),ed.set(this,void 0),ex(this,eu,e,"f")}static createResponse(e,t,s){let n=new sz(t);return n._run(()=>n._createOrRetrieveResponse(e,t,{...s,headers:{...s?.headers,"X-Stainless-Helper-Method":"stream"}})),n}async _createOrRetrieveResponse(e,t,s){let n,r=s?.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",()=>this.controller.abort())),eS(this,ec,"m",ef).call(this);let i=null;for await(let r of("response_id"in t?(n=await e.responses.retrieve(t.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),i=t.starting_after??null):n=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal}),this._connected(),n))eS(this,ec,"m",ep).call(this,r,i);if(n.controller.signal?.aborted)throw new ek;return eS(this,ec,"m",em).call(this)}[(eu=new WeakMap,eh=new WeakMap,ed=new WeakMap,ec=new WeakSet,ef=function(){this.ended||ex(this,eh,void 0,"f")},ep=function(e,t){if(this.ended)return;let s=(e,s)=>{(null==t||s.sequence_number>t)&&this._emit(e,s)},n=eS(this,ec,"m",eg).call(this,e);switch(s("event",e),e.type){case"response.output_text.delta":{let t=n.output[e.output_index];if(!t)throw new eO(`missing output at index ${e.output_index}`);if("message"===t.type){let n=t.content[e.content_index];if(!n)throw new eO(`missing content at index ${e.content_index}`);if("output_text"!==n.type)throw new eO(`expected content to be 'output_text', got ${n.type}`);s("response.output_text.delta",{...e,snapshot:n.text})}break}case"response.function_call_arguments.delta":{let t=n.output[e.output_index];if(!t)throw new eO(`missing output at index ${e.output_index}`);"function_call"===t.type&&s("response.function_call_arguments.delta",{...e,snapshot:t.arguments});break}default:s(e.type,e)}},em=function(){if(this.ended)throw new eO("stream has ended, this shouldn't happen");let e=eS(this,eh,"f");if(!e)throw new eO("request ended without sending any events");ex(this,eh,void 0,"f");let t=function(e,t){var s;return t&&(s=t,tZ(s.text?.format))?sK(e,t):{...e,output_parsed:null,output:e.output.map(e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map(e=>({...e,parsed:null}))}:e)}}(e,eS(this,eu,"f"));return ex(this,ed,t,"f"),t},eg=function(e){let t=eS(this,eh,"f");if(!t){if("response.created"!==e.type)throw new eO(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return ex(this,eh,e.response,"f")}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{let s=t.output[e.output_index];if(!s)throw new eO(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{let s=t.output[e.output_index];if(!s)throw new eO(`missing output at index ${e.output_index}`);if("message"===s.type){let t=s.content[e.content_index];if(!t)throw new eO(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new eO(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{let s=t.output[e.output_index];if(!s)throw new eO(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":ex(this,eh,e.response,"f")}return t},Symbol.asyncIterator)](){let e=[],t=[],s=!1;return this.on("event",s=>{let n=t.shift();n?n.resolve(s):e.push(s)}),this.on("end",()=>{for(let e of(s=!0,t))e.resolve(void 0);t.length=0}),this.on("abort",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),this.on("error",e=>{for(let n of(s=!0,t))n.reject(e);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((e,s)=>t.push({resolve:e,reject:s})).then(e=>e?{value:e,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();let e=eS(this,ed,"f");if(!e)throw new eO("stream ended without producing a ChatCompletion");return e}}class sQ extends tH{list(e,t={},s){return this._client.getAPIList(tV`/responses/${e}/input_items`,tk,{query:t,...s})}}class sG extends tH{constructor(){super(...arguments),this.inputItems=new sQ(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap(e=>("object"in e&&"response"===e.object&&sV(e),e))}retrieve(e,t={},s){return this._client.get(tV`/responses/${e}`,{query:t,...s,stream:t?.stream??!1})}delete(e,t){return this._client.delete(tV`/responses/${e}`,{...t,headers:so([{Accept:"*/*"},t?.headers])})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap(t=>sK(t,e))}stream(e,t){return sz.createResponse(this._client,e,t)}cancel(e,t){return this._client.post(tV`/responses/${e}/cancel`,t)}}sG.InputItems=sQ;class sY extends tH{create(e,t,s){return this._client.post(tV`/uploads/${e}/parts`,tN({body:t,...s},this._client))}}class sZ extends tH{constructor(){super(...arguments),this.parts=new sY(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(tV`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(tV`/uploads/${e}/complete`,{body:t,...s})}}sZ.Parts=sY;let s0=async e=>{let t=await Promise.allSettled(e),s=t.filter(e=>"rejected"===e.status);if(s.length){for(let e of s)console.error(e.reason);throw Error(`${s.length} promise(s) failed - see the above errors`)}let n=[];for(let e of t)"fulfilled"===e.status&&n.push(e.value);return n};class s1 extends tH{create(e,t,s){return this._client.post(tV`/vector_stores/${e}/file_batches`,{body:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(tV`/vector_stores/${n}/file_batches/${e}`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}cancel(e,t,s){let{vector_store_id:n}=t;return this._client.post(tV`/vector_stores/${n}/file_batches/${e}/cancel`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s){let{vector_store_id:n,...r}=t;return this._client.getAPIList(tV`/vector_stores/${n}/file_batches/${e}/files`,tk,{query:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async poll(e,t,s){let n=so([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let{data:r,response:i}=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=i.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await eK(a);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");let r=Math.min(n?.maxConcurrency??5,t.length),i=this._client,a=t.values(),o=[...s];async function l(e){for(let t of e){let e=await i.files.create({file:t,purpose:"assistants"},n);o.push(e.id)}}let c=Array(r).fill(a).map(l);return await s0(c),await this.createAndPoll(e,{file_ids:o})}}class s2 extends tH{create(e,t,s){return this._client.post(tV`/vector_stores/${e}/files`,{body:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}retrieve(e,t,s){let{vector_store_id:n}=t;return this._client.get(tV`/vector_stores/${n}/files/${e}`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}update(e,t,s){let{vector_store_id:n,...r}=t;return this._client.post(tV`/vector_stores/${n}/files/${e}`,{body:r,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e,t={},s){return this._client.getAPIList(tV`/vector_stores/${e}/files`,tk,{query:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}delete(e,t,s){let{vector_store_id:n}=t;return this._client.delete(tV`/vector_stores/${n}/files/${e}`,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}async createAndPoll(e,t,s){let n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){let n=so([s?.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":s?.pollIntervalMs?.toString()??void 0}]);for(;;){let r=await this.retrieve(t,{vector_store_id:e},{...s,headers:n}).withResponse(),i=r.data;switch(i.status){case"in_progress":let a=5e3;if(s?.pollIntervalMs)a=s.pollIntervalMs;else{let e=r.response.headers.get("openai-poll-after-ms");if(e){let t=parseInt(e);isNaN(t)||(a=t)}}await eK(a);break;case"failed":case"completed":return i}}}async upload(e,t,s){let n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){let n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){let{vector_store_id:n}=t;return this._client.getAPIList(tV`/vector_stores/${n}/files/${e}/content`,tR,{...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}class s4 extends tH{constructor(){super(...arguments),this.files=new s2(this._client),this.fileBatches=new s1(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}retrieve(e,t){return this._client.get(tV`/vector_stores/${e}`,{...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}update(e,t,s){return this._client.post(tV`/vector_stores/${e}`,{body:t,...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}list(e={},t){return this._client.getAPIList("/vector_stores",tk,{query:e,...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}delete(e,t){return this._client.delete(tV`/vector_stores/${e}`,{...t,headers:so([{"OpenAI-Beta":"assistants=v2"},t?.headers])})}search(e,t,s){return this._client.getAPIList(tV`/vector_stores/${e}/search`,tR,{body:t,method:"post",...s,headers:so([{"OpenAI-Beta":"assistants=v2"},s?.headers])})}}s4.Files=s2,s4.FileBatches=s1;class s3{constructor({baseURL:e=sb("OPENAI_BASE_URL"),apiKey:t=sb("OPENAI_API_KEY"),organization:s=sb("OPENAI_ORG_ID")??null,project:n=sb("OPENAI_PROJECT_ID")??null,...r}={}){if(e_.set(this,void 0),this.completions=new sA(this),this.chat=new sr(this),this.embeddings=new sk(this),this.files=new sT(this),this.images=new sX(this),this.audio=new sh(this),this.moderations=new sH(this),this.models=new sJ(this),this.fineTuning=new sU(this),this.graders=new sF(this),this.vectorStores=new s4(this),this.beta=new s$(this),this.batches=new sd(this),this.uploads=new sZ(this),this.responses=new sG(this),this.evals=new sC(this),this.containers=new sR(this),void 0===t)throw new eO("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");let i={apiKey:t,organization:s,project:n,...r,baseURL:e||"https://api.openai.com/v1"};if(!i.dangerouslyAllowBrowser&&e4())throw new eO("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");this.baseURL=i.baseURL,this.timeout=i.timeout??ey.DEFAULT_TIMEOUT,this.logger=i.logger??console;let a="warn";this.logLevel=a,this.logLevel=ez(i.logLevel,"ClientOptions.logLevel",this)??ez(sb("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??a,this.fetchOptions=i.fetchOptions,this.maxRetries=i.maxRetries??2,this.fetch=i.fetch??function(){if("undefined"!=typeof fetch)return fetch;throw Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}(),ex(this,e_,ts,"f"),this._options=i,this.apiKey=t,this.organization=s,this.project=n}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:t}){}authHeaders(e){return so([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return function(e,t={}){let s,n,r=e,i=function(e=tp){let t;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.encodeDotInKeys&&"boolean"!=typeof e.encodeDotInKeys)throw TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw TypeError("Encoder has to be a function.");let s=e.charset||tp.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=tn;if(void 0!==e.format){if(!tl.call(tr,e.format))throw TypeError("Unknown format option provided.");n=e.format}let r=tr[n],i=tp.filter;if(("function"==typeof e.filter||tu(e.filter))&&(i=e.filter),t=e.arrayFormat&&e.arrayFormat in tc?e.arrayFormat:"indices"in e?e.indices?"indices":"repeat":tp.arrayFormat,"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw TypeError("`commaRoundTrip` must be a boolean, or absent");let a=void 0===e.allowDots?!0==!!e.encodeDotInKeys||tp.allowDots:!!e.allowDots;return{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:tp.addQueryPrefix,allowDots:a,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:tp.allowEmptyArrays,arrayFormat:t,charset:s,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:tp.charsetSentinel,commaRoundTrip:!!e.commaRoundTrip,delimiter:void 0===e.delimiter?tp.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:tp.encode,encodeDotInKeys:"boolean"==typeof e.encodeDotInKeys?e.encodeDotInKeys:tp.encodeDotInKeys,encoder:"function"==typeof e.encoder?e.encoder:tp.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:tp.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:tp.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:tp.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:tp.strictNullHandling}}(t);"function"==typeof i.filter?r=(0,i.filter)("",r):tu(i.filter)&&(s=i.filter);let a=[];if("object"!=typeof r||null===r)return"";let o=tc[i.arrayFormat],l="comma"===o&&i.commaRoundTrip;s||(s=Object.keys(r)),i.sort&&s.sort(i.sort);let c=new WeakMap;for(let e=0;e<s.length;++e){let t=s[e];i.skipNulls&&null===r[t]||td(a,function e(t,s,n,r,i,a,o,l,c,u,h,d,f,p,m,g,y,_){var w,b;let v,x=t,S=_,$=0,A=!1;for(;void 0!==(S=S.get(tm))&&!A;){let e=S.get(t);if($+=1,void 0!==e)if(e===$)throw RangeError("Cyclic object value");else A=!0;void 0===S.get(tm)&&($=0)}if("function"==typeof u?x=u(s,x):x instanceof Date?x=f?.(x):"comma"===n&&tu(x)&&(x=to(x,function(e){return e instanceof Date?f?.(e):e})),null===x){if(a)return c&&!g?c(s,tp.encoder,y,"key",p):s;x=""}if("string"==typeof(w=x)||"number"==typeof w||"boolean"==typeof w||"symbol"==typeof w||"bigint"==typeof w||(b=x)&&"object"==typeof b&&b.constructor&&b.constructor.isBuffer&&b.constructor.isBuffer(b)){if(c){let e=g?s:c(s,tp.encoder,y,"key",p);return[m?.(e)+"="+m?.(c(x,tp.encoder,y,"value",p))]}return[m?.(s)+"="+m?.(String(x))]}let I=[];if(void 0===x)return I;if("comma"===n&&tu(x))g&&c&&(x=to(x,c)),v=[{value:x.length>0?x.join(",")||null:void 0}];else if(tu(u))v=u;else{let e=Object.keys(x);v=h?e.sort(h):e}let O=l?String(s).replace(/\./g,"%2E"):String(s),R=r&&tu(x)&&1===x.length?O+"[]":O;if(i&&tu(x)&&0===x.length)return R+"[]";for(let s=0;s<v.length;++s){let w=v[s],b="object"==typeof w&&void 0!==w.value?w.value:x[w];if(o&&null===b)continue;let S=d&&l?w.replace(/\./g,"%2E"):w,A=tu(x)?"function"==typeof n?n(R,S):R:R+(d?"."+S:"["+S+"]");_.set(t,$);let O=new WeakMap;O.set(tm,_),td(I,e(b,A,n,r,i,a,o,l,"comma"===n&&g&&tu(x)?null:c,u,h,d,f,p,m,g,y,O))}return I}(r[t],t,o,l,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}let u=a.join(i.delimiter),h=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),u.length>0?h+u:""}(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${e2}`}defaultIdempotencyKey(){return`stainless-node-retry-${e$()}`}makeStatusError(e,t,s,n){return eR.generate(e,t,s,n)}buildURL(e,t){let s=new URL(eF(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(n)&&(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then(s=>({method:e,path:t,...s})))}request(e,t=null){return new tA(this,this.makeRequest(e,t,void 0))}async makeRequest(e,t,s){let n=await e,r=n.maxRetries??this.maxRetries;null==t&&(t=r),await this.prepareOptions(n);let{req:i,url:a,timeout:o}=this.buildRequest(n,{retryCount:r-t});await this.prepareRequest(i,{url:a,options:n});let l="log_"+(0x1000000*Math.random()|0).toString(16).padStart(6,"0"),c=void 0===s?"":`, retryOf: ${s}`,u=Date.now();if(e0(this).debug(`[${l}] sending request`,e1({retryOfRequestLogID:s,method:n.method,url:a,options:n,headers:i.headers})),n.signal?.aborted)throw new ek;let h=new AbortController,d=await this.fetchWithTimeout(a,i,o,h).catch(eI),f=Date.now();if(d instanceof Error){let e=`retrying, ${t} attempts remaining`;if(n.signal?.aborted)throw new ek;let r=eA(d)||/timed? ?out/i.test(String(d)+("cause"in d?String(d.cause):""));if(t)return e0(this).info(`[${l}] connection ${r?"timed out":"failed"} - ${e}`),e0(this).debug(`[${l}] connection ${r?"timed out":"failed"} (${e})`,e1({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),this.retryRequest(n,t,s??l);if(e0(this).info(`[${l}] connection ${r?"timed out":"failed"} - error; no more retries left`),e0(this).debug(`[${l}] connection ${r?"timed out":"failed"} (error; no more retries left)`,e1({retryOfRequestLogID:s,url:a,durationMs:f-u,message:d.message})),r)throw new eP;throw new eE({cause:d})}let p=[...d.headers.entries()].filter(([e])=>"x-request-id"===e).map(([e,t])=>", "+e+": "+JSON.stringify(t)).join(""),m=`[${l}${c}${p}] ${i.method} ${a} ${d.ok?"succeeded":"failed"} with status ${d.status} in ${f-u}ms`;if(!d.ok){let e=this.shouldRetry(d);if(t&&e){let e=`retrying, ${t} attempts remaining`;return await tt(d.body),e0(this).info(`${m} - ${e}`),e0(this).debug(`[${l}] response error (${e})`,e1({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),this.retryRequest(n,t,s??l,d.headers)}let r=e?"error; no more retries left":"error; not retryable";e0(this).info(`${m} - ${r}`);let i=await d.text().catch(e=>eI(e).message),a=eH(i),o=a?void 0:i;throw e0(this).debug(`[${l}] response error (${r})`,e1({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,message:o,durationMs:Date.now()-u})),this.makeStatusError(d.status,a,o,d.headers)}return e0(this).info(m),e0(this).debug(`[${l}] response start`,e1({retryOfRequestLogID:s,url:d.url,status:d.status,headers:d.headers,durationMs:f-u})),{response:d,options:n,controller:h,requestLogID:l,retryOfRequestLogID:s,startTime:u}}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}requestAPIList(e,t){return new tO(this,this.makeRequest(t,null,void 0),e)}async fetchWithTimeout(e,t,s,n){let{signal:r,method:i,...a}=t||{};r&&r.addEventListener("abort",()=>n.abort());let o=setTimeout(()=>n.abort(),s),l=globalThis.ReadableStream&&a.body instanceof globalThis.ReadableStream||"object"==typeof a.body&&null!==a.body&&Symbol.asyncIterator in a.body,c={signal:n.signal,...l?{duplex:"half"}:{},method:"GET",...a};i&&(c.method=i.toUpperCase());try{return await this.fetch.call(void 0,e,c)}finally{clearTimeout(o)}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||!!(e.status>=500))}async retryRequest(e,t,s,n){let r,i=n?.get("retry-after-ms");if(i){let e=parseFloat(i);Number.isNaN(e)||(r=e)}let a=n?.get("retry-after");if(a&&!r){let e=parseFloat(a);r=Number.isNaN(e)?Date.parse(a)-Date.now():1e3*e}if(!(r&&0<=r&&r<6e4)){let s=e.maxRetries??this.maxRetries;r=this.calculateDefaultRetryTimeoutMillis(t,s)}return await eK(r),this.makeRequest(e,t-1,s)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}buildRequest(e,{retryCount:t=0}={}){let s={...e},{method:n,path:r,query:i}=s,a=this.buildURL(r,i);"timeout"in s&&eJ("timeout",s.timeout),s.timeout=s.timeout??this.timeout;let{bodyHeaders:o,body:l}=this.buildBody({options:s}),c=this.buildHeaders({options:e,method:n,bodyHeaders:o,retryCount:t});return{req:{method:n,headers:c,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&l instanceof globalThis.ReadableStream&&{duplex:"half"},...l&&{body:l},...this.fetchOptions??{},...s.fetchOptions??{}},url:a,timeout:s.timeout}}buildHeaders({options:e,method:t,bodyHeaders:s,retryCount:n}){let r={};this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),r[this.idempotencyHeader]=e.idempotencyKey);let i=so([r,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(n),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...e5(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(i),i.values}buildBody({options:{body:e,headers:t}}){if(!e)return{bodyHeaders:void 0,body:void 0};let s=so([t]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||"string"==typeof e&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:"object"==typeof e&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&"function"==typeof e.next)?{bodyHeaders:void 0,body:e7(e)}:eS(this,e_,"f").call(this,{body:e,headers:s})}}ey=s3,e_=new WeakMap,s3.OpenAI=ey,s3.DEFAULT_TIMEOUT=6e5,s3.OpenAIError=eO,s3.APIError=eR,s3.APIConnectionError=eE,s3.APIConnectionTimeoutError=eP,s3.APIUserAbortError=ek,s3.NotFoundError=eM,s3.ConflictError=ej,s3.RateLimitError=eD,s3.BadRequestError=eC,s3.AuthenticationError=eT,s3.InternalServerError=eB,s3.PermissionDeniedError=eN,s3.UnprocessableEntityError=eL,s3.toFile=tX,s3.Completions=sA,s3.Chat=sr,s3.Embeddings=sk,s3.Files=sT,s3.Images=sX,s3.Audio=sh,s3.Moderations=sH,s3.Models=sJ,s3.FineTuning=sU,s3.Graders=sF,s3.VectorStores=s4,s3.Beta=s$,s3.Batches=sd,s3.Uploads=sZ,s3.Responses=sG,s3.Evals=sC,s3.Containers=sR}};
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(5155);r(2115);var n=r(9708),a=r(2085),i=r(9434);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:a,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:a,className:t})),...c})}},3738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(5155),n=r(2108),a=r(5695),i=r(2115),o=r(285),d=r(6695);function c(){let e=(0,a.useRouter)(),[t,r]=(0,i.useState)(!1);(0,i.useEffect)(()=>{(0,n.getSession)().then(t=>{t&&e.push("/dashboard")})},[e]);let c=async e=>{r(!0);try{await (0,n.signIn)(e,{callbackUrl:"/dashboard"})}catch(e){console.error("Sign in error:",e)}finally{r(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,s.jsxs)(d.aR,{className:"text-center",children:[(0,s.jsx)(d.ZB,{className:"text-3xl font-bold",children:"AIntegrity"}),(0,s.jsx)(d.BT,{children:"Sign in to access your AI assistant with persistent knowledge base"})]}),(0,s.jsxs)(d.Wu,{className:"space-y-4",children:[(0,s.jsx)(o.$,{onClick:()=>c("google"),disabled:t,className:"w-full",variant:"outline",children:t?"Signing in...":"Continue with Google"}),(0,s.jsx)(o.$,{onClick:()=>c("github"),disabled:t,className:"w-full",variant:"outline",children:t?"Signing in...":"Continue with GitHub"})]})]})})}},5695:(e,t,r)=>{"use strict";var s=r(8999);r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}})},5942:(e,t,r)=>{Promise.resolve().then(r.bind(r,3738))},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>a,aR:()=>i});var s=r(5155);r(2115);var n=r(9434);function a(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(2596),n=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,441,684,358],()=>t(5942)),_N_E=e.O()}]);
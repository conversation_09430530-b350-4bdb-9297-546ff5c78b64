(()=>{var e={};e.id=105,e.ids=[105],e.modules={2769:(e,t,r)=>{"use strict";r.d(t,{ChatInterface:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ChatInterface() from the server but ChatInterface is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\chat-interface.tsx","ChatInterface")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,80559)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56753:(e,t,r)=>{Promise.resolve().then(r.bind(r,73801))},57425:(e,t,r)=>{Promise.resolve().then(r.bind(r,2769))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73801:(e,t,r)=>{"use strict";r.d(t,{ChatInterface:()=>h});var s=r(60687),a=r(43210),i=r(29523),n=r(89667),o=r(44493),l=r(62688);let d=(0,l.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var c=r(58869);let u=(0,l.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var p=r(4780);function h(){let[e,t]=(0,a.useState)([]),[r,l]=(0,a.useState)(""),[h,x]=(0,a.useState)(!1),[m,f]=(0,a.useState)(null),y=(0,a.useRef)(null),g=async()=>{if(!r.trim()||h)return;let e={id:Date.now().toString(),content:r,role:"user",timestamp:new Date};t(t=>[...t,e]),l(""),x(!0);try{let e=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:r,chatId:m})});if(!e.ok)throw Error("Failed to send message");let s=await e.json(),a={id:(Date.now()+1).toString(),content:s.response,role:"assistant",timestamp:new Date};t(e=>[...e,a]),f(s.chatId)}catch(r){console.error("Error sending message:",r);let e={id:(Date.now()+1).toString(),content:"Sorry, there was an error processing your message. Please try again.",role:"assistant",timestamp:new Date};t(t=>[...t,e])}finally{x(!1)}};return(0,s.jsxs)("div",{className:"flex flex-col h-full max-w-4xl mx-auto p-6",children:[(0,s.jsxs)("div",{className:"flex-1 overflow-auto space-y-4 mb-4",children:[0===e.length?(0,s.jsxs)("div",{className:"text-center text-gray-500 mt-8",children:[(0,s.jsx)(d,{className:"mx-auto h-12 w-12 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Start a conversation"}),(0,s.jsx)("p",{children:"Ask me anything! I can help you with questions and access your saved notes."})]}):e.map(e=>(0,s.jsxs)("div",{className:(0,p.cn)("flex gap-3","user"===e.role?"justify-end":"justify-start"),children:["assistant"===e.role&&(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(d,{className:"w-4 h-4 text-white"})})}),(0,s.jsx)(o.Zp,{className:(0,p.cn)("max-w-[80%] p-3","user"===e.role?"bg-blue-500 text-white":"bg-white"),children:(0,s.jsx)("p",{className:"whitespace-pre-wrap",children:e.content})}),"user"===e.role&&(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(c.A,{className:"w-4 h-4 text-white"})})})]},e.id)),h&&(0,s.jsxs)("div",{className:"flex gap-3 justify-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,s.jsx)(d,{className:"w-4 h-4 text-white"})})}),(0,s.jsx)(o.Zp,{className:"max-w-[80%] p-3 bg-white",children:(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,s.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),(0,s.jsx)("div",{ref:y})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.p,{value:r,onChange:e=>l(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),g())},placeholder:"Type your message...",disabled:h,className:"flex-1"}),(0,s.jsx)(i.$,{onClick:g,disabled:h||!r.trim(),children:(0,s.jsx)(u,{className:"w-4 h-4"})})]})]})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80559:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),a=r(2769);function i(){return(0,s.jsx)("div",{className:"h-full",children:(0,s.jsx)(a.ChatInterface,{})})}},81630:e=>{"use strict";e.exports=require("http")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,374,310,658,924,342,377],()=>r(24188));module.exports=s})();
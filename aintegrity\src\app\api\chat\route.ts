import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { generateChatCompletion } from '@/lib/openai'
import type { Note } from '@prisma/client'

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { message, chatId } = await req.json()

    if (!message) {
      return NextResponse.json({ error: 'Message is required' }, { status: 400 })
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Get or create chat
    let chat
    if (chatId) {
      chat = await prisma.chat.findFirst({
        where: { id: chatId, userId: user.id },
        include: { messages: { orderBy: { createdAt: 'asc' } } }
      })
    }

    if (!chat) {
      chat = await prisma.chat.create({
        data: {
          userId: user.id,
          title: message.slice(0, 50) + (message.length > 50 ? '...' : ''),
        },
        include: { messages: true }
      })
    }

    // Save user message
    await prisma.message.create({
      data: {
        content: message,
        role: 'user',
        chatId: chat.id,
      }
    })

    // Get relevant notes for context
    let context = ''
    try {
      // Find similar notes (simplified - in production you'd use pgvector)
      const notes = await prisma.note.findMany({
        where: { userId: user.id },
        take: 3,
        orderBy: { updatedAt: 'desc' }
      })

      if (notes.length > 0) {
        context = notes.map((note: Note) => `${note.title}: ${note.content}`).join('\n\n')
      }
    } catch (error) {
      console.error('Error getting context:', error)
    }

    // Generate AI response
    const messages = chat.messages.map(msg => ({
      role: msg.role as 'user' | 'assistant',
      content: msg.content
    }))
    
    messages.push({ role: 'user', content: message })

    const aiResponse = await generateChatCompletion(messages, context)

    // Save AI response
    await prisma.message.create({
      data: {
        content: aiResponse,
        role: 'assistant',
        chatId: chat.id,
      }
    })

    return NextResponse.json({
      response: aiResponse,
      chatId: chat.id,
    })

  } catch (error) {
    console.error('Chat API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

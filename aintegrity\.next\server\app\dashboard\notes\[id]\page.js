(()=>{var e={};e.id=815,e.ids=[815],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5522:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["dashboard",{children:["notes",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6695)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\notes\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\notes\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/dashboard/notes/[id]/page",pathname:"/dashboard/notes/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},6695:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413),i=r(17939);async function a({params:e}){let{id:t}=await e;return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Edit Note"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Update your knowledge base"})]}),(0,s.jsx)(i.NoteEditor,{noteId:t})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},17939:(e,t,r)=>{"use strict";r.d(t,{NoteEditor:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call NoteEditor() from the server but NoteEditor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\note-editor.tsx","NoteEditor")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28315:(e,t,r)=>{Promise.resolve().then(r.bind(r,56808))},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56808:(e,t,r)=>{"use strict";r.d(t,{NoteEditor:()=>b});var s=r(60687),i=r(43210),a=r(16189),n=r(29523),o=r(89667),d=r(4780);function l({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,d.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}var c=r(44493),u=r(96834),p=r(62688);let x=(0,p.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),h=(0,p.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),v=(0,p.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),m=(0,p.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function b({noteId:e}){let t=(0,a.useRouter)(),[r,d]=(0,i.useState)(""),[p,b]=(0,i.useState)(""),[g,f]=(0,i.useState)([]),[y,j]=(0,i.useState)(""),[k,w]=(0,i.useState)(!1),[N,E]=(0,i.useState)(!1),[C,P]=(0,i.useState)(!1);(0,i.useCallback)(async()=>{if(e){E(!0);try{let t=await fetch(`/api/notes/${e}`);if(t.ok){let e=await t.json();d(e.title),b(e.content),f(e.tags),w(e.isPublic)}}catch(e){console.error("Error fetching note:",e)}finally{E(!1)}}},[e]);let q=async()=>{if(!r.trim()||!p.trim())return void alert("Please fill in both title and content");P(!0);try{let s=e?`/api/notes/${e}`:"/api/notes",i=e?"PUT":"POST";(await fetch(s,{method:i,headers:{"Content-Type":"application/json"},body:JSON.stringify({title:r,content:p,tags:g,isPublic:k})})).ok?t.push("/dashboard/notes"):alert("Error saving note")}catch(e){console.error("Error saving note:",e),alert("Error saving note")}finally{P(!1)}},A=async()=>{if(e&&confirm("Are you sure you want to delete this note?"))try{(await fetch(`/api/notes/${e}`,{method:"DELETE"})).ok?t.push("/dashboard/notes"):alert("Error deleting note")}catch(e){console.error("Error deleting note:",e),alert("Error deleting note")}},_=()=>{y.trim()&&!g.includes(y.trim())&&(f([...g,y.trim()]),j(""))},D=e=>{f(g.filter(t=>t!==e))};return N?(0,s.jsx)(c.Zp,{children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-32 bg-gray-200 rounded"})]})})}):(0,s.jsxs)(c.Zp,{children:[(0,s.jsx)(c.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(c.ZB,{children:e?"Edit Note":"Create Note"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(n.$,{variant:"outline",onClick:()=>t.back(),children:[(0,s.jsx)(x,{className:"mr-2 h-4 w-4"}),"Back"]}),e&&(0,s.jsxs)(n.$,{variant:"destructive",onClick:A,children:[(0,s.jsx)(h,{className:"mr-2 h-4 w-4"}),"Delete"]}),(0,s.jsxs)(n.$,{onClick:q,disabled:C,children:[(0,s.jsx)(v,{className:"mr-2 h-4 w-4"}),C?"Saving...":"Save"]})]})]})}),(0,s.jsxs)(c.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Title"}),(0,s.jsx)(o.p,{value:r,onChange:e=>d(e.target.value),placeholder:"Enter note title..."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Content"}),(0,s.jsx)(l,{value:p,onChange:e=>b(e.target.value),placeholder:"Write your note content...",rows:12})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Tags"}),(0,s.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,s.jsx)(o.p,{value:y,onChange:e=>j(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),_())},placeholder:"Add a tag...",className:"flex-1"}),(0,s.jsx)(n.$,{onClick:_,variant:"outline",children:"Add"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:g.map(e=>(0,s.jsxs)(u.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,s.jsx)(m,{className:"h-3 w-3 cursor-pointer",onClick:()=>D(e)})]},e))})]})]})]})}},62659:(e,t,r)=>{Promise.resolve().then(r.bind(r,17939))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(60687);r(43210);var i=r(4780);function a({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(60687);r(43210);var i=r(8730),a=r(24224),n=r(4780);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...a}){let d=r?i.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:t}),e),...a})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,374,310,658,924,645,377],()=>r(5522));module.exports=s})();
(()=>{var e={};e.id=191,e.ids=[191],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8796:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["dashboard",{children:["notes",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52413)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\notes\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,63144)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\dashboard\\notes\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/notes/page",pathname:"/dashboard/notes",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21505:(e,r,t)=>{Promise.resolve().then(t.bind(t,27637))},27637:(e,r,t)=>{"use strict";t.d(r,{NotesGrid:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call NotesGrid() from the server but NotesGrid is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\notes-grid.tsx","NotesGrid")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34657:(e,r,t)=>{Promise.resolve().then(t.bind(t,94711))},52413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413),a=t(27637);function i(){return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Notes"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage your knowledge base"})]}),(0,s.jsx)(a.NotesGrid,{})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94711:(e,r,t)=>{"use strict";t.d(r,{NotesGrid:()=>p});var s=t(60687),a=t(43210),i=t(85814),n=t.n(i),o=t(44493),d=t(96834),l=t(29523),c=t(96474),u=t(10022);function p(){let[e,r]=(0,a.useState)([]),[t,i]=(0,a.useState)(!0);return t?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,r)=>(0,s.jsxs)(o.Zp,{className:"animate-pulse",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-5/6"})]})})]},r))}):(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(l.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard/notes/new",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Create New Note"]})})}),0===e.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(u.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notes yet"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first note to start building your knowledge base."}),(0,s.jsx)(l.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard/notes/new",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Create Note"]})})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>(0,s.jsx)(n(),{href:`/dashboard/notes/${e.id}`,children:(0,s.jsxs)(o.Zp,{className:"hover:shadow-md transition-shadow cursor-pointer h-full",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{className:"text-lg line-clamp-2",children:e.title})}),(0,s.jsxs)(o.Wu,{children:[(0,s.jsxs)("p",{className:"text-gray-600 text-sm line-clamp-3 mb-3",children:[e.content.slice(0,150),"..."]}),e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 mb-3",children:[e.tags.slice(0,3).map(e=>(0,s.jsx)(d.E,{variant:"secondary",className:"text-xs",children:e},e)),e.tags.length>3&&(0,s.jsxs)(d.E,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Updated ",new Date(e.updatedAt).toLocaleDateString()]})]})]})},e.id))})]})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96834:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var s=t(60687);t(43210);var a=t(8730),i=t(24224),n=t(4780);let o=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,asChild:t=!1,...i}){let d=t?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(o({variant:r}),e),...i})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,374,310,658,924,342,377],()=>t(8796));module.exports=s})();
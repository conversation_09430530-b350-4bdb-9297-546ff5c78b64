{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport GoogleProvider from 'next-auth/providers/google'\nimport GitHubProvider from 'next-auth/providers/github'\nimport { prisma } from './db'\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID || '',\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_ID || '',\n      clientSecret: process.env.GITHUB_SECRET || '',\n    }),\n  ],\n  callbacks: {\n    session: async ({ session, token }) => {\n      if (session?.user && token?.sub) {\n        session.user.id = token.sub\n      }\n      return session\n    },\n    jwt: async ({ user, token }) => {\n      if (user) {\n        token.uid = user.id\n      }\n      return token\n    },\n  },\n  session: {\n    strategy: 'jwt',\n  },\n  pages: {\n    signIn: '/auth/signin',\n  },\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,oJAAA,CAAA,gBAAa,AAAD,EAAE,gHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB,IAAI;YAC1C,cAAc,QAAQ,GAAG,CAAC,oBAAoB,IAAI;QACpD;QACA,CAAA,GAAA,mJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,SAAS,IAAI;YACnC,cAAc,QAAQ,GAAG,CAAC,aAAa,IAAI;QAC7C;KACD;IACD,WAAW;QACT,SAAS,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE;YAChC,IAAI,SAAS,QAAQ,OAAO,KAAK;gBAC/B,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,KAAK,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;YACzB,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/AIntegrity/aintegrity/src/app/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\n\nexport default async function Home() {\n  const session = await getServerSession(authOptions)\n\n  if (session) {\n    redirect('/dashboard')\n  } else {\n    redirect('/auth/signin')\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEe,eAAe;IAC5B,MAAM,UAAU,MAAM,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD,EAAE,kHAAA,CAAA,cAAW;IAElD,IAAI,SAAS;QACX,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX,OAAO;QACL,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IACX;AACF", "debugId": null}}]}
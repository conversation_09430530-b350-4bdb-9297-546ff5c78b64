# AIntegrity - AI Chat with Knowledge Base

A modern ChatGPT wrapper with persistent knowledge base and note-taking capabilities. Built with Next.js, Prisma, and OpenAI.

## 🚀 Quick Start

1. **Clone and Install**
   ```bash
   cd aintegrity
   npm install
   ```

2. **Set up Environment Variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

3. **Set up Database**
   ```bash
   npm run db:push
   npm run db:generate
   ```

4. **Run Development Server**
   ```bash
   npm run dev
   ```

Visit `http://localhost:3000` to see your application.

## 📚 Documentation

- [Setup Guide](./docs/SETUP.md) - Complete setup instructions
- [Architecture](./docs/ARCHITECTURE.md) - System architecture overview
- [API Reference](./docs/API.md) - API endpoints documentation
- [Deployment](./docs/DEPLOYMENT.md) - Production deployment guide
- [Development](./docs/DEVELOPMENT.md) - Development workflow and best practices

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 19, Tai<PERSON><PERSON> CSS, ShadCN/UI
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL with pgvector (for embeddings)
- **Authentication**: NextAuth.js with Google/GitHub OAuth
- **AI**: OpenAI GPT-4 and text-embedding-3-small
- **Deployment**: Vercel (recommended)

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## 📁 Project Structure

```
aintegrity/
├── src/
│   ├── app/                 # Next.js App Router pages
│   ├── components/          # React components
│   ├── lib/                 # Utility functions and configurations
│   └── types/               # TypeScript type definitions
├── prisma/                  # Database schema and migrations
├── docs/                    # Documentation
└── public/                  # Static assets
```

## 🤝 Contributing

See [DEVELOPMENT.md](./docs/DEVELOPMENT.md) for development guidelines.

## 📄 License

MIT License - see LICENSE file for details.

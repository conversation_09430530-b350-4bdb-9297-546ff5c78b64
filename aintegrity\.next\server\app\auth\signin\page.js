(()=>{var e={};e.id=680,e.ids=[680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(49384),i=r(82348);function s(...e){return(0,i.QP)((0,n.$)(e))}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var n=r(60687);r(43210);var i=r(8730),s=r(24224),o=r(4780);let a=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:s=!1,...d}){let l=s?i.DX:"button";return(0,n.jsx)(l,{"data-slot":"button",className:(0,o.cn)(a({variant:t,size:r,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},42455:(e,t,r)=>{Promise.resolve().then(r.bind(r,80415))},43476:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(60687),i=r(82136),s=r(16189),o=r(43210),a=r(29523),d=r(44493);function l(){(0,s.useRouter)();let[e,t]=(0,o.useState)(!1),r=async e=>{t(!0);try{await (0,i.signIn)(e,{callbackUrl:"/dashboard"})}catch(e){console.error("Sign in error:",e)}finally{t(!1)}};return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)(d.Zp,{className:"w-full max-w-md",children:[(0,n.jsxs)(d.aR,{className:"text-center",children:[(0,n.jsx)(d.ZB,{className:"text-3xl font-bold",children:"AIntegrity"}),(0,n.jsx)(d.BT,{children:"Sign in to access your AI assistant with persistent knowledge base"})]}),(0,n.jsxs)(d.Wu,{className:"space-y-4",children:[(0,n.jsx)(a.$,{onClick:()=>r("google"),disabled:e,className:"w-full",variant:"outline",children:e?"Signing in...":"Continue with Google"}),(0,n.jsx)(a.$,{onClick:()=>r("github"),disabled:e,className:"w-full",variant:"outline",children:e?"Signing in...":"Continue with GitHub"})]})]})})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>a,Zp:()=>s,aR:()=>o});var n=r(60687);r(43210);var i=r(4780);function s({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function a({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}},48031:(e,t,r)=>{Promise.resolve().then(r.bind(r,83305))},51266:(e,t,r)=>{Promise.resolve().then(r.bind(r,87578))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64904:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var n=r(65239),i=r(48088),s=r(88170),o=r.n(s),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87578)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\auth\\signin\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\auth\\signin\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},69418:(e,t,r)=>{Promise.resolve().then(r.bind(r,43476))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73711:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},76926:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return d}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=s?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(61120));function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}let s={current:null},o="function"==typeof n.cache?n.cache:e=>e,a=console.warn;function d(e){return function(...t){a(e(...t))}}o(e=>{try{a(s.current)}finally{s.current=null}})},79551:e=>{"use strict";e.exports=require("url")},80415:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>s});var n=r(60687),i=r(82136);function s({children:e}){return(0,n.jsx)(i.SessionProvider,{children:e})}},81959:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},83305:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\providers\\session-provider.tsx","SessionProvider")},87578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AIntegrity\\\\aintegrity\\\\src\\\\app\\\\auth\\\\signin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\auth\\signin\\page.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var n=r(37413),i=r(22376),s=r.n(i),o=r(68726),a=r.n(o),d=r(83305);r(61135);let l={title:"AIntegrity - AI Chat with Knowledge Base",description:"A ChatGPT wrapper with persistent knowledge base and note-taking capabilities"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${s().variable} ${a().variable} antialiased`,children:(0,n.jsx)(d.SessionProvider,{children:e})})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[243,310,658,924],()=>r(64904));module.exports=n})();
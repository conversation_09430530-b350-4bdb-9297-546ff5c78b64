(()=>{var e={};e.id=681,e.ids=[681],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(16467),n=t(36344),i=t(65752),o=t(5069);let a={adapter:(0,s.y)(o.z),providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET})],callbacks:{session:async({session:e,token:r})=>(e?.user&&r?.sub&&(e.user.id=r.sub),e),jwt:async({user:e,token:r})=>(e&&(r.uid=e.id),r)},session:{strategy:"jwt"},pages:{signIn:"/auth/signin"}}},18514:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>x});var n=t(96559),i=t(48088),o=t(37719),a=t(32190),u=t(19854),c=t(12909),p=t(5069),l=t(33509);async function d(){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let r=await p.z.user.findUnique({where:{email:e.user.email}});if(!r)return a.NextResponse.json({error:"User not found"},{status:404});let t=await p.z.note.findMany({where:{userId:r.id},orderBy:{updatedAt:"desc"},select:{id:!0,title:!0,content:!0,summary:!0,tags:!0,isPublic:!0,createdAt:!0,updatedAt:!0}});return a.NextResponse.json(t)}catch(e){return console.error("Notes GET error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{title:t,content:s,tags:n=[],isPublic:i=!1}=await e.json();if(!t||!s)return a.NextResponse.json({error:"Title and content are required"},{status:400});let o=await p.z.user.findUnique({where:{email:r.user.email}});if(!o)return a.NextResponse.json({error:"User not found"},{status:404});let d=null;try{let e=await (0,l.Lu)(`${t} ${s}`);d=JSON.stringify(e)}catch(e){console.error("Error generating embedding:",e)}let x=await p.z.note.create({data:{title:t,content:s,tags:n,isPublic:i,embedding:d,userId:o.id}});return a.NextResponse.json(x)}catch(e){return console.error("Notes POST error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/notes/route",pathname:"/api/notes",filename:"route",bundlePath:"app/api/notes/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\api\\notes\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:v,serverHooks:y}=m;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:v})}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33509:(e,r,t)=>{"use strict";t.d(r,{Lu:()=>i,kY:()=>o});var s=t(40276);if(!process.env.OPENAI_API_KEY)throw Error("Missing OPENAI_API_KEY environment variable");let n=new s.Ay({apiKey:process.env.OPENAI_API_KEY});async function i(e){return(await n.embeddings.create({model:"text-embedding-3-small",input:e})).data[0].embedding}async function o(e,r){let t=r?{role:"system",content:`You are a helpful AI assistant. Use the following context from the user's notes to inform your responses when relevant:

${r}`}:{role:"system",content:"You are a helpful AI assistant."},s=await n.chat.completions.create({model:"gpt-4o-mini",messages:[t,...e],temperature:.7,max_tokens:2e3});return s.choices[0]?.message?.content||""}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,706,580,641],()=>t(18514));module.exports=s})();
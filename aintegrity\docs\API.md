# API Reference

Complete API documentation for AIntegrity backend endpoints.

## Authentication

All API endpoints (except auth) require authentication. Include session cookie or JWT token.

### Error Responses

All endpoints return consistent error format:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## Authentication Endpoints

### NextAuth.js Handlers

**Base URL:** `/api/auth`

These are handled automatically by NextAuth.js:

- `GET /api/auth/signin` - Sign in page
- `POST /api/auth/signin/[provider]` - OA<PERSON> sign in
- `GET /api/auth/callback/[provider]` - OAuth callback
- `POST /api/auth/signout` - Sign out
- `GET /api/auth/session` - Get current session

## Chat Endpoints

### Send Message

**Endpoint:** `POST /api/chat`

Send a message to the AI assistant and get a response.

**Request Body:**
```json
{
  "message": "What is machine learning?",
  "chatId": "optional-existing-chat-id"
}
```

**Response:**
```json
{
  "response": "Machine learning is a subset of artificial intelligence...",
  "chatId": "chat_123456789"
}
```

**Behavior:**
- If `chatId` is provided, adds to existing conversation
- If `chatId` is null/missing, creates new chat
- Searches user's notes for relevant context
- Includes context in AI prompt for better responses

## Notes Endpoints

### List Notes

**Endpoint:** `GET /api/notes`

Get all notes for the authenticated user.

**Query Parameters:**
- `limit` (optional) - Number of notes to return (default: 50)
- `offset` (optional) - Number of notes to skip (default: 0)
- `search` (optional) - Search term to filter notes

**Response:**
```json
[
  {
    "id": "note_123",
    "title": "Machine Learning Basics",
    "content": "Machine learning is...",
    "summary": "Overview of ML concepts",
    "tags": ["ai", "ml", "tech"],
    "isPublic": false,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-02T00:00:00Z"
  }
]
```

### Create Note

**Endpoint:** `POST /api/notes`

Create a new note.

**Request Body:**
```json
{
  "title": "My New Note",
  "content": "This is the content of my note...",
  "tags": ["tag1", "tag2"],
  "isPublic": false
}
```

**Response:**
```json
{
  "id": "note_123",
  "title": "My New Note",
  "content": "This is the content of my note...",
  "summary": null,
  "tags": ["tag1", "tag2"],
  "isPublic": false,
  "embedding": "[0.1, 0.2, ...]",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z"
}
```

**Behavior:**
- Automatically generates embeddings for the note
- Creates summary if content is long enough
- Tags are optional and can be empty array

### Get Note

**Endpoint:** `GET /api/notes/[id]`

Get a specific note by ID.

**Response:**
```json
{
  "id": "note_123",
  "title": "Machine Learning Basics",
  "content": "Machine learning is...",
  "summary": "Overview of ML concepts",
  "tags": ["ai", "ml", "tech"],
  "isPublic": false,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-02T00:00:00Z"
}
```

### Update Note

**Endpoint:** `PUT /api/notes/[id]`

Update an existing note.

**Request Body:**
```json
{
  "title": "Updated Title",
  "content": "Updated content...",
  "tags": ["new", "tags"],
  "isPublic": true
}
```

**Response:**
```json
{
  "id": "note_123",
  "title": "Updated Title",
  "content": "Updated content...",
  "summary": "Updated summary",
  "tags": ["new", "tags"],
  "isPublic": true,
  "embedding": "[0.1, 0.2, ...]",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-03T00:00:00Z"
}
```

**Behavior:**
- Regenerates embeddings if content changed
- Updates summary if content significantly changed
- All fields are optional (partial updates supported)

### Delete Note

**Endpoint:** `DELETE /api/notes/[id]`

Delete a note permanently.

**Response:**
```json
{
  "success": true
}
```

## Data Models

### User
```typescript
interface User {
  id: string
  name?: string
  email: string
  emailVerified?: Date
  image?: string
  createdAt: Date
  updatedAt: Date
}
```

### Note
```typescript
interface Note {
  id: string
  title: string
  content: string
  summary?: string
  embedding?: string  // JSON array of numbers
  tags: string[]
  isPublic: boolean
  createdAt: Date
  updatedAt: Date
  userId: string
}
```

### Chat
```typescript
interface Chat {
  id: string
  title?: string
  createdAt: Date
  updatedAt: Date
  userId: string
  messages: Message[]
}
```

### Message
```typescript
interface Message {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  createdAt: Date
  chatId: string
}
```

## Rate Limiting

Currently no rate limiting is implemented, but consider adding:

- 100 requests per minute per user for notes
- 20 requests per minute per user for chat
- 1000 requests per hour per IP address

## Webhooks

No webhooks are currently implemented, but future considerations:

- Note created/updated/deleted events
- Chat message events
- User registration events

## SDK/Client Libraries

Currently no official SDKs, but the API follows REST conventions and can be used with any HTTP client:

```javascript
// Example using fetch
const response = await fetch('/api/notes', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    title: 'My Note',
    content: 'Note content...'
  })
})

const note = await response.json()
```

## OpenAPI Specification

Future enhancement: Generate OpenAPI/Swagger specification for automatic client generation and interactive documentation.

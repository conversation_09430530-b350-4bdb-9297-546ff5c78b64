(()=>{var e={};e.id=185,e.ids=[185],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},8368:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{DELETE:()=>f,GET:()=>l,PUT:()=>x});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(19854),c=t(12909),p=t(5069),d=t(33509);async function l(e,{params:r}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await p.z.user.findUnique({where:{email:e.user.email}});if(!t)return a.NextResponse.json({error:"User not found"},{status:404});let{id:s}=await r,n=await p.z.note.findFirst({where:{id:s,userId:t.id}});if(!n)return a.NextResponse.json({error:"Note not found"},{status:404});return a.NextResponse.json(n)}catch(e){return console.error("Note GET error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function x(e,{params:r}){try{let t=await (0,u.getServerSession)(c.N);if(!t?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{title:s,content:n,tags:o,isPublic:i}=await e.json(),l=await p.z.user.findUnique({where:{email:t.user.email}});if(!l)return a.NextResponse.json({error:"User not found"},{status:404});let{id:x}=await r,f=await p.z.note.findFirst({where:{id:x,userId:l.id}});if(!f)return a.NextResponse.json({error:"Note not found"},{status:404});let m=f.embedding;if(s!==f.title||n!==f.content)try{let e=await (0,d.Lu)(`${s} ${n}`);m=JSON.stringify(e)}catch(e){console.error("Error generating embedding:",e)}let w=await p.z.note.update({where:{id:x},data:{title:s,content:n,tags:o,isPublic:i,embedding:m}});return a.NextResponse.json(w)}catch(e){return console.error("Note PUT error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:r}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=await p.z.user.findUnique({where:{email:e.user.email}});if(!t)return a.NextResponse.json({error:"User not found"},{status:404});let{id:s}=await r;if(!await p.z.note.findFirst({where:{id:s,userId:t.id}}))return a.NextResponse.json({error:"Note not found"},{status:404});return await p.z.note.delete({where:{id:s}}),a.NextResponse.json({success:!0})}catch(e){return console.error("Note DELETE error:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/notes/[id]/route",pathname:"/api/notes/[id]",filename:"route",bundlePath:"app/api/notes/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\api\\notes\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:h,serverHooks:g}=m;function v(){return(0,i.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(16467),n=t(36344),o=t(65752),i=t(5069);let a={adapter:(0,s.y)(i.z),providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,o.A)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET})],callbacks:{session:async({session:e,token:r})=>(e?.user&&r?.sub&&(e.user.id=r.sub),e),jwt:async({user:e,token:r})=>(e&&(r.uid=e.id),r)},session:{strategy:"jwt"},pages:{signIn:"/auth/signin"}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33509:(e,r,t)=>{"use strict";t.d(r,{Lu:()=>o,kY:()=>i});var s=t(40276);if(!process.env.OPENAI_API_KEY)throw Error("Missing OPENAI_API_KEY environment variable");let n=new s.Ay({apiKey:process.env.OPENAI_API_KEY});async function o(e){return(await n.embeddings.create({model:"text-embedding-3-small",input:e})).data[0].embedding}async function i(e,r){let t=r?{role:"system",content:`You are a helpful AI assistant. Use the following context from the user's notes to inform your responses when relevant:

${r}`}:{role:"system",content:"You are a helpful AI assistant."},s=await n.chat.completions.create({model:"gpt-4o-mini",messages:[t,...e],temperature:.7,max_tokens:2e3});return s.choices[0]?.message?.content||""}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,706,580,641],()=>t(8368));module.exports=s})();
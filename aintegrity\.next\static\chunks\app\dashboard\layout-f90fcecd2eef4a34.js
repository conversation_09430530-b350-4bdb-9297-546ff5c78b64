(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o});var a=s(5155);s(2115);var r=s(9708),n=s(2085),i=s(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:n,asChild:o=!1,...l}=e,c=o?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:s,size:n,className:t})),...l})}},690:(e,t,s)=>{"use strict";s.d(t,{Header:()=>N});var a=s(5155),r=s(2108),n=s(2115),i=s(4011),d=s(9434);function o(e){let{className:t,...s}=e;return(0,a.jsx)(i.bL,{"data-slot":"avatar",className:(0,d.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)(i._V,{"data-slot":"avatar-image",className:(0,d.cn)("aspect-square size-full",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(i.H4,{"data-slot":"avatar-fallback",className:(0,d.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}var u=s(285),m=s(8279);function h(e){let{...t}=e;return(0,a.jsx)(m.bL,{"data-slot":"dropdown-menu",...t})}function v(e){let{...t}=e;return(0,a.jsx)(m.l9,{"data-slot":"dropdown-menu-trigger",...t})}function x(e){let{className:t,sideOffset:s=4,...r}=e;return(0,a.jsx)(m.ZL,{children:(0,a.jsx)(m.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function f(e){let{className:t,inset:s,variant:r="default",...n}=e;return(0,a.jsx)(m.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":r,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}var g=s(1007),b=s(4835),p=s(2098),j=s(3509);function w(){let[e,t]=(0,n.useState)(!0);return(0,n.useEffect)(()=>{let e=localStorage.getItem("theme");e&&(t("dark"===e),document.documentElement.className=e)},[]),(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let s=e?"light":"dark";t(!e),document.documentElement.className=s,localStorage.setItem("theme",s)},className:"w-9 h-9 p-0",children:[e?(0,a.jsx)(p.A,{className:"h-4 w-4"}):(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}function N(){var e,t,s,n,i,d;let{data:m}=(0,r.useSession)();return(0,a.jsx)("header",{className:"bg-gray-800 shadow-sm border-b border-gray-700 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h2",{className:"text-lg font-semibold text-white",children:"Dashboard"})}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(w,{}),(0,a.jsxs)(h,{children:[(0,a.jsx)(v,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsxs)(o,{className:"h-8 w-8",children:[(0,a.jsx)(l,{src:(null==m||null==(e=m.user)?void 0:e.image)||"",alt:(null==m||null==(t=m.user)?void 0:t.name)||""}),(0,a.jsx)(c,{children:(null==m||null==(n=m.user)||null==(s=n.name)?void 0:s.charAt(0))||(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})})}),(0,a.jsxs)(x,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsxs)(f,{className:"flex flex-col items-start",children:[(0,a.jsx)("div",{className:"font-medium",children:null==m||null==(i=m.user)?void 0:i.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:null==m||null==(d=m.user)?void 0:d.email})]}),(0,a.jsxs)(f,{onClick:()=>{(0,r.signOut)({callbackUrl:"/auth/signin"})},children:[(0,a.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})]})]})]})]})})}},4017:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>h});var a=s(5155),r=s(6874),n=s.n(r),i=s(5695),d=s(1497),o=s(7434),l=s(4616),c=s(285),u=s(9434);let m=[{name:"Chat",href:"/dashboard",icon:d.A},{name:"Notes",href:"/dashboard/notes",icon:o.A}];function h(){let e=(0,i.usePathname)();return(0,a.jsxs)("div",{className:"w-64 bg-gray-800 shadow-sm border-r border-gray-700 flex flex-col",children:[(0,a.jsx)("div",{className:"p-4",children:(0,a.jsx)("h1",{className:"text-xl font-bold text-white",children:"AIntegrity"})}),(0,a.jsx)("nav",{className:"flex-1 px-4 space-y-2",children:m.map(t=>{let s=e===t.href;return(0,a.jsxs)(n(),{href:t.href,className:(0,u.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-gray-700 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,a.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),t.name]},t.name)})}),(0,a.jsx)("div",{className:"p-4 border-t border-gray-700",children:(0,a.jsx)(c.$,{asChild:!0,className:"w-full",children:(0,a.jsxs)(n(),{href:"/dashboard/notes/new",children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"New Note"]})})})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},9447:(e,t,s)=>{Promise.resolve().then(s.bind(s,690)),Promise.resolve().then(s.bind(s,4017))}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,440,294,441,684,358],()=>t(9447)),_N_E=e.O()}]);
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[733],{285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155);r(2115);var i=r(9708),n=r(2085),s=r(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...l}=e,c=d?i.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,s.cn)(o({variant:r,size:n,className:t})),...l})}},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155);r(2115);var i=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},5695:(e,t,r)=>{"use strict";var a=r(8999);r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},6126:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var a=r(5155);r(2115);var i=r(9708),n=r(2085),s=r(9434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:t,variant:r,asChild:n=!1,...d}=e,l=n?i.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(o({variant:r}),t),...d})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>s});var a=r(5155);r(2115);var i=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...r})}},7029:(e,t,r)=>{"use strict";r.d(t,{NoteEditor:()=>p});var a=r(5155),i=r(2115),n=r(5695),s=r(285),o=r(2523),d=r(9434);function l(e){let{className:t,...r}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,d.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...r})}var c=r(6695),u=r(6126),v=r(9946);let h=(0,v.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),g=(0,v.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]),f=(0,v.A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),x=(0,v.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function p(e){let{noteId:t}=e,r=(0,n.useRouter)(),[d,v]=(0,i.useState)(""),[p,m]=(0,i.useState)(""),[b,y]=(0,i.useState)([]),[k,w]=(0,i.useState)(""),[j,N]=(0,i.useState)(!1),[E,C]=(0,i.useState)(!1),[A,S]=(0,i.useState)(!1),z=(0,i.useCallback)(async()=>{if(t){C(!0);try{let e=await fetch("/api/notes/".concat(t));if(e.ok){let t=await e.json();v(t.title),m(t.content),y(t.tags),N(t.isPublic)}}catch(e){console.error("Error fetching note:",e)}finally{C(!1)}}},[t]);(0,i.useEffect)(()=>{t&&z()},[t,z]);let P=async()=>{if(!d.trim()||!p.trim())return void alert("Please fill in both title and content");S(!0);try{let e=t?"/api/notes/".concat(t):"/api/notes",a=t?"PUT":"POST";(await fetch(e,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify({title:d,content:p,tags:b,isPublic:j})})).ok?r.push("/dashboard/notes"):alert("Error saving note")}catch(e){console.error("Error saving note:",e),alert("Error saving note")}finally{S(!1)}},_=async()=>{if(t&&confirm("Are you sure you want to delete this note?"))try{(await fetch("/api/notes/".concat(t),{method:"DELETE"})).ok?r.push("/dashboard/notes"):alert("Error deleting note")}catch(e){console.error("Error deleting note:",e),alert("Error deleting note")}},M=()=>{k.trim()&&!b.includes(k.trim())&&(y([...b,k.trim()]),w(""))},R=e=>{y(b.filter(t=>t!==e))};return E?(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),(0,a.jsx)("div",{className:"h-10 bg-gray-200 rounded"}),(0,a.jsx)("div",{className:"h-32 bg-gray-200 rounded"})]})})}):(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(c.ZB,{children:t?"Edit Note":"Create Note"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(s.$,{variant:"outline",onClick:()=>r.back(),children:[(0,a.jsx)(h,{className:"mr-2 h-4 w-4"}),"Back"]}),t&&(0,a.jsxs)(s.$,{variant:"destructive",onClick:_,children:[(0,a.jsx)(g,{className:"mr-2 h-4 w-4"}),"Delete"]}),(0,a.jsxs)(s.$,{onClick:P,disabled:A,children:[(0,a.jsx)(f,{className:"mr-2 h-4 w-4"}),A?"Saving...":"Save"]})]})]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Title"}),(0,a.jsx)(o.p,{value:d,onChange:e=>v(e.target.value),placeholder:"Enter note title..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Content"}),(0,a.jsx)(l,{value:p,onChange:e=>m(e.target.value),placeholder:"Write your note content...",rows:12})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Tags"}),(0,a.jsxs)("div",{className:"flex gap-2 mb-2",children:[(0,a.jsx)(o.p,{value:k,onChange:e=>w(e.target.value),onKeyPress:e=>{"Enter"===e.key&&(e.preventDefault(),M())},placeholder:"Add a tag...",className:"flex-1"}),(0,a.jsx)(s.$,{onClick:M,variant:"outline",children:"Add"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:b.map(e=>(0,a.jsxs)(u.E,{variant:"secondary",className:"flex items-center gap-1",children:[e,(0,a.jsx)(x,{className:"h-3 w-3 cursor-pointer",onClick:()=>R(e)})]},e))})]})]})]})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),i=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,a.$)(t))}},9733:(e,t,r)=>{Promise.resolve().then(r.bind(r,7029))},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(2115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:n=2,absoluteStrokeWidth:s,className:c="",children:u,iconNode:v,...h}=e;return(0,a.createElement)("svg",{ref:t,...l,width:i,height:i,stroke:r,strokeWidth:s?24*Number(n)/Number(i):n,className:o("lucide",c),...!u&&!d(h)&&{"aria-hidden":"true"},...h},[...v.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:d,...l}=r;return(0,a.createElement)(c,{ref:n,iconNode:t,className:o("lucide-".concat(i(s(e))),"lucide-".concat(e),d),...l})});return r.displayName=s(e),r}}}]);
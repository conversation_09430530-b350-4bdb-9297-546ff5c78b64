exports.id=377,exports.ids=[377],exports.modules={2849:(e,t,s)=>{"use strict";s.d(t,{Header:()=>y});var r=s(60687),n=s(82136),a=s(43210),i=s(11096),o=s(4780);function d({className:e,...t}){return(0,r.jsx)(i.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function l({className:e,...t}){return(0,r.jsx)(i._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",e),...t})}function c({className:e,...t}){return(0,r.jsx)(i.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var u=s(29523),m=s(40591);function v({...e}){return(0,r.jsx)(m.bL,{"data-slot":"dropdown-menu",...e})}function h({...e}){return(0,r.jsx)(m.l9,{"data-slot":"dropdown-menu-trigger",...e})}function x({className:e,sideOffset:t=4,...s}){return(0,r.jsx)(m.ZL,{children:(0,r.jsx)(m.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...s})})}function f({className:e,inset:t,variant:s="default",...n}){return(0,r.jsx)(m.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":s,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}var g=s(58869),b=s(40083),p=s(21134),j=s(363);function w(){let[e,t]=(0,a.useState)(!0);return(0,r.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{let s=e?"light":"dark";t(!e),document.documentElement.className=s,localStorage.setItem("theme",s)},className:"w-9 h-9 p-0",children:[e?(0,r.jsx)(p.A,{className:"h-4 w-4"}):(0,r.jsx)(j.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}function y(){let{data:e}=(0,n.useSession)();return(0,r.jsx)("header",{className:"bg-gray-800 shadow-sm border-b border-gray-700 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("div",{children:(0,r.jsx)("h2",{className:"text-lg font-semibold text-white",children:"Dashboard"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(w,{}),(0,r.jsxs)(v,{children:[(0,r.jsx)(h,{asChild:!0,children:(0,r.jsx)(u.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsxs)(d,{className:"h-8 w-8",children:[(0,r.jsx)(l,{src:e?.user?.image||"",alt:e?.user?.name||""}),(0,r.jsx)(c,{children:e?.user?.name?.charAt(0)||(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})})}),(0,r.jsxs)(x,{className:"w-56",align:"end",forceMount:!0,children:[(0,r.jsxs)(f,{className:"flex flex-col items-start",children:[(0,r.jsx)("div",{className:"font-medium",children:e?.user?.name}),(0,r.jsx)("div",{className:"text-xs text-muted-foreground",children:e?.user?.email})]}),(0,r.jsxs)(f,{onClick:()=>{(0,n.signOut)({callbackUrl:"/auth/signin"})},children:[(0,r.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})]})]})]})]})})}},4780:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var r=s(49384),n=s(82348);function a(...e){return(0,n.QP)((0,r.$)(e))}},5069:(e,t,s)=>{"use strict";s.d(t,{z:()=>n});var r=s(96330);let n=globalThis.prisma??new r.PrismaClient},12909:(e,t,s)=>{"use strict";s.d(t,{N:()=>o});var r=s(16467),n=s(36344),a=s(65752),i=s(5069);let o={adapter:(0,r.y)(i.z),providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID||"",clientSecret:process.env.GOOGLE_CLIENT_SECRET||""}),(0,a.A)({clientId:process.env.GITHUB_ID||"",clientSecret:process.env.GITHUB_SECRET||""})],callbacks:{session:async({session:e,token:t})=>(e?.user&&t?.sub&&(e.user.id=t.sub),e),jwt:async({user:e,token:t})=>(e&&(t.uid=e.id),t)},session:{strategy:"jwt"},pages:{signIn:"/auth/signin"}}},19049:(e,t,s)=>{Promise.resolve().then(s.bind(s,2849)),Promise.resolve().then(s.bind(s,75227))},28777:(e,t,s)=>{Promise.resolve().then(s.bind(s,95354)),Promise.resolve().then(s.bind(s,70445))},29523:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(60687);s(43210);var n=s(8730),a=s(24224),i=s(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:s,asChild:a=!1,...d}){let l=a?n.DX:"button";return(0,r.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:s,className:e})),...d})}},42455:(e,t,s)=>{Promise.resolve().then(s.bind(s,80415))},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>i});var r=s(60687);s(43210);var n=s(4780);function a({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},48031:(e,t,s)=>{Promise.resolve().then(s.bind(s,83305))},61135:()=>{},63144:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(37413),n=s(39916),a=s(19854),i=s(12909),o=s(70445),d=s(95354);async function l({children:e}){return await (0,a.getServerSession)(i.N)||(0,n.redirect)("/auth/signin"),(0,r.jsxs)("div",{className:"flex h-screen bg-gray-900",children:[(0,r.jsx)(o.Sidebar,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(d.Header,{}),(0,r.jsx)("main",{className:"flex-1 overflow-auto bg-gray-900",children:e})]})]})}},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70445:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\sidebar.tsx","Sidebar")},73711:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},75227:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>v});var r=s(60687),n=s(85814),a=s.n(n),i=s(16189),o=s(58887),d=s(10022),l=s(96474),c=s(29523),u=s(4780);let m=[{name:"Chat",href:"/dashboard",icon:o.A},{name:"Notes",href:"/dashboard/notes",icon:d.A}];function v(){let e=(0,i.usePathname)();return(0,r.jsxs)("div",{className:"w-64 bg-gray-800 shadow-sm border-r border-gray-700 flex flex-col",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"AIntegrity"})}),(0,r.jsx)("nav",{className:"flex-1 px-4 space-y-2",children:m.map(t=>{let s=e===t.href;return(0,r.jsxs)(a(),{href:t.href,className:(0,u.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-gray-700 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"),children:[(0,r.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),t.name]},t.name)})}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-700",children:(0,r.jsx)(c.$,{asChild:!0,className:"w-full",children:(0,r.jsxs)(a(),{href:"/dashboard/notes/new",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"New Note"]})})})]})}},80415:(e,t,s)=>{"use strict";s.d(t,{SessionProvider:()=>a});var r=s(60687),n=s(82136);function a({children:e}){return(0,r.jsx)(n.SessionProvider,{children:e})}},81959:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},83305:(e,t,s)=>{"use strict";s.d(t,{SessionProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\providers\\session-provider.tsx","SessionProvider")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>l});var r=s(37413),n=s(22376),a=s.n(n),i=s(68726),o=s.n(i),d=s(83305);s(61135);let l={title:"AIntegrity - AI Chat with Knowledge Base",description:"A ChatGPT wrapper with persistent knowledge base and note-taking capabilities"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:(0,r.jsx)(d.SessionProvider,{children:e})})})}},95354:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\header.tsx","Header")}};
exports.id=377,exports.ids=[377],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(49384),n=r(82348);function a(...e){return(0,n.QP)((0,s.$)(e))}},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(16467),n=r(36344),a=r(65752),i=r(5069);let o={adapter:(0,s.y)(i.z),providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET})],callbacks:{session:async({session:e,token:t})=>(e?.user&&t?.sub&&(e.user.id=t.sub),e),jwt:async({user:e,token:t})=>(e&&(t.uid=e.id),t)},session:{strategy:"jwt"},pages:{signIn:"/auth/signin"}}},19049:(e,t,r)=>{Promise.resolve().then(r.bind(r,71130)),Promise.resolve().then(r.bind(r,75227))},28777:(e,t,r)=>{Promise.resolve().then(r.bind(r,95354)),Promise.resolve().then(r.bind(r,70445))},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687);r(43210);var n=r(8730),a=r(24224),i=r(4780);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:a=!1,...d}){let l=a?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},42455:(e,t,r)=>{Promise.resolve().then(r.bind(r,80415))},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>a,aR:()=>i});var s=r(60687);r(43210);var n=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}},48031:(e,t,r)=>{Promise.resolve().then(r.bind(r,83305))},61135:()=>{},63144:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(37413),n=r(39916),a=r(19854),i=r(12909),o=r(70445),d=r(95354);async function l({children:e}){return await (0,a.getServerSession)(i.N)||(0,n.redirect)("/auth/signin"),(0,s.jsxs)("div",{className:"flex h-screen bg-gray-100",children:[(0,s.jsx)(o.Sidebar,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)(d.Header,{}),(0,s.jsx)("main",{className:"flex-1 overflow-auto",children:e})]})]})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70445:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\sidebar.tsx","Sidebar")},71130:(e,t,r)=>{"use strict";r.d(t,{Header:()=>g});var s=r(60687),n=r(82136);r(43210);var a=r(11096),i=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function d({className:e,...t}){return(0,s.jsx)(a._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",e),...t})}function l({className:e,...t}){return(0,s.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}var c=r(29523),u=r(40591);function v({...e}){return(0,s.jsx)(u.bL,{"data-slot":"dropdown-menu",...e})}function m({...e}){return(0,s.jsx)(u.l9,{"data-slot":"dropdown-menu-trigger",...e})}function h({className:e,sideOffset:t=4,...r}){return(0,s.jsx)(u.ZL,{children:(0,s.jsx)(u.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function f({className:e,inset:t,variant:r="default",...n}){return(0,s.jsx)(u.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}var x=r(58869),b=r(40083);function g(){let{data:e}=(0,n.useSession)();return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Dashboard"})}),(0,s.jsxs)(v,{children:[(0,s.jsx)(m,{asChild:!0,children:(0,s.jsx)(c.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(o,{className:"h-8 w-8",children:[(0,s.jsx)(d,{src:e?.user?.image||"",alt:e?.user?.name||""}),(0,s.jsx)(l,{children:e?.user?.name?.charAt(0)||(0,s.jsx)(x.A,{className:"h-4 w-4"})})]})})}),(0,s.jsxs)(h,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsxs)(f,{className:"flex flex-col items-start",children:[(0,s.jsx)("div",{className:"font-medium",children:e?.user?.name}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e?.user?.email})]}),(0,s.jsxs)(f,{onClick:()=>{(0,n.signOut)({callbackUrl:"/auth/signin"})},children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})]})]})]})})}},73711:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},75227:(e,t,r)=>{"use strict";r.d(t,{Sidebar:()=>m});var s=r(60687),n=r(85814),a=r.n(n),i=r(16189),o=r(58887),d=r(10022),l=r(96474),c=r(29523),u=r(4780);let v=[{name:"Chat",href:"/dashboard",icon:o.A},{name:"Notes",href:"/dashboard/notes",icon:d.A}];function m(){let e=(0,i.usePathname)();return(0,s.jsxs)("div",{className:"w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col",children:[(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"AIntegrity"})}),(0,s.jsx)("nav",{className:"flex-1 px-4 space-y-2",children:v.map(t=>{let r=e===t.href;return(0,s.jsxs)(a(),{href:t.href,className:(0,u.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",r?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,s.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),t.name]},t.name)})}),(0,s.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,s.jsx)(c.$,{asChild:!0,className:"w-full",children:(0,s.jsxs)(a(),{href:"/dashboard/notes/new",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"New Note"]})})})]})}},80415:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>a});var s=r(60687),n=r(82136);function a({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},81959:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},83305:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\providers\\session-provider.tsx","SessionProvider")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var s=r(37413),n=r(22376),a=r.n(n),i=r(68726),o=r.n(i),d=r(83305);r(61135);let l={title:"AIntegrity - AI Chat with Knowledge Base",description:"A ChatGPT wrapper with persistent knowledge base and note-taking capabilities"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:(0,s.jsx)(d.SessionProvider,{children:e})})})}},95354:(e,t,r)=>{"use strict";r.d(t,{Header:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\components\\dashboard\\header.tsx","Header")}};
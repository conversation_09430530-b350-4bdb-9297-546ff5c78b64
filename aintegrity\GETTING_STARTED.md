# Getting Started with AIntegrity

Welcome to AIntegrity! This guide will help you get up and running quickly.

## 🎯 What is AIntegrity?

AIntegrity is a ChatGPT wrapper with a persistent knowledge base. It combines the power of AI conversation with your personal notes to provide contextually aware responses.

### Key Benefits
- **Personalized AI** - AI that knows your notes and preferences
- **Knowledge Management** - Organize and search your thoughts
- **Seamless Integration** - Chat and notes work together
- **Privacy Focused** - Your data stays secure and private

## 🚀 Quick Setup (5 minutes)

### 1. Prerequisites
Make sure you have:
- Node.js 18+ installed
- A PostgreSQL database (we recommend Supabase for beginners)
- An OpenAI API key

### 2. Clone and Install
```bash
git clone <your-repo-url>
cd aintegrity
npm install
```

### 3. Environment Setup
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your values
nano .env  # or use your preferred editor
```

### 4. Database Setup
```bash
# Push the schema to your database
npm run db:push

# Generate the Prisma client
npm run db:generate
```

### 5. Start Development Server
```bash
npm run dev
```

Visit `http://localhost:3000` and you're ready to go! 🎉

## 📝 First Steps

### 1. Sign In
- Click "Sign In" on the homepage
- Choose Google or GitHub authentication
- Complete the OAuth flow

### 2. Create Your First Note
- Navigate to "Notes" in the sidebar
- Click "Create New Note"
- Add a title and content
- Save your note

### 3. Start Chatting
- Go back to the "Chat" section
- Ask the AI about your note
- Watch as it provides contextually aware responses

## 🛠 Configuration Options

### Database Options

#### Option A: Supabase (Recommended for beginners)
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Get your connection string from Settings > Database
4. Add to your `.env` file

#### Option B: Local PostgreSQL
1. Install PostgreSQL locally
2. Create a database: `createdb aintegrity`
3. Use connection string: `postgresql://username:password@localhost:5432/aintegrity`

### OpenAI Setup
1. Go to [platform.openai.com](https://platform.openai.com)
2. Create an API key
3. Add to your `.env` file as `OPENAI_API_KEY`

### OAuth Setup (Optional)
For Google and GitHub sign-in:

#### Google OAuth
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create OAuth 2.0 credentials
3. Add redirect URI: `http://localhost:3000/api/auth/callback/google`
4. Add client ID and secret to `.env`

#### GitHub OAuth
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create new OAuth app
3. Add callback URL: `http://localhost:3000/api/auth/callback/github`
4. Add client ID and secret to `.env`

## 💡 Usage Tips

### Effective Note-Taking
- **Use descriptive titles** - Help the AI understand your content
- **Add relevant tags** - Organize your knowledge
- **Write clear content** - Better content = better AI responses
- **Link related concepts** - Create connections between ideas

### Better AI Conversations
- **Reference your notes** - Ask about specific topics you've saved
- **Be specific** - Clear questions get better answers
- **Build on conversations** - Continue discussions across sessions
- **Provide context** - Help the AI understand what you need

### Organization Strategies
- **Use consistent tags** - Develop a tagging system
- **Regular reviews** - Update and refine your notes
- **Search effectively** - Use keywords and semantic search
- **Archive old content** - Keep your knowledge base current

## 🔧 Customization

### Environment Variables
```bash
# Required
DATABASE_URL="your-database-url"
NEXTAUTH_SECRET="your-secret-key"
OPENAI_API_KEY="your-openai-key"

# Optional
GOOGLE_CLIENT_ID="your-google-id"
GOOGLE_CLIENT_SECRET="your-google-secret"
GITHUB_ID="your-github-id"
GITHUB_SECRET="your-github-secret"
```

### Database Commands
```bash
# View your data
npm run db:studio

# Reset database (careful!)
npm run db:push --force-reset

# Create migration
npm run db:migrate
```

### Development Commands
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run linting
npm run lint

# Type checking
npx tsc --noEmit
```

## 🎨 Customizing the UI

### Themes
The app uses system theme by default (dark/light mode). You can customize colors in:
- `src/app/globals.css` - CSS variables
- `tailwind.config.js` - Tailwind configuration

### Components
All UI components are in `src/components/`:
- `ui/` - ShadCN/UI components
- `dashboard/` - App-specific components

### Adding New Components
```bash
# Add ShadCN component
npx shadcn@latest add dialog

# Create custom component
touch src/components/my-component.tsx
```

## 📚 Learning Resources

### Documentation
- [Setup Guide](./docs/SETUP.md) - Detailed setup instructions
- [Architecture](./docs/ARCHITECTURE.md) - How the system works
- [API Reference](./docs/API.md) - API documentation
- [Development](./docs/DEVELOPMENT.md) - Development guidelines

### Technologies Used
- **Next.js** - [nextjs.org](https://nextjs.org)
- **Prisma** - [prisma.io](https://prisma.io)
- **NextAuth.js** - [next-auth.js.org](https://next-auth.js.org)
- **Tailwind CSS** - [tailwindcss.com](https://tailwindcss.com)
- **ShadCN/UI** - [ui.shadcn.com](https://ui.shadcn.com)

## 🆘 Troubleshooting

### Common Issues

#### "Database connection failed"
- Check your `DATABASE_URL` in `.env`
- Ensure your database is running
- Verify network connectivity

#### "OpenAI API error"
- Check your `OPENAI_API_KEY` in `.env`
- Verify you have credits in your OpenAI account
- Ensure the API key has proper permissions

#### "OAuth not working"
- Check client ID and secret in `.env`
- Verify redirect URIs in OAuth app settings
- Ensure OAuth apps are not in development mode

#### "Build errors"
- Clear node_modules: `rm -rf node_modules package-lock.json && npm install`
- Clear Next.js cache: `rm -rf .next`
- Check for TypeScript errors: `npx tsc --noEmit`

### Getting Help
1. Check the [documentation](./docs/)
2. Search existing [GitHub issues](https://github.com/your-repo/issues)
3. Create a new issue with details
4. Join our community discussions

## 🚀 Next Steps

Once you have the basic setup working:

1. **Explore Features** - Try all the functionality
2. **Add Content** - Create notes and have conversations
3. **Customize** - Modify the UI to your preferences
4. **Deploy** - Follow the [deployment guide](./docs/DEPLOYMENT.md)
5. **Contribute** - Help improve the project

## 📈 Scaling Up

### For Production Use
- Set up proper environment variables
- Use a production database
- Configure OAuth for your domain
- Set up monitoring and logging
- Implement backup strategies

### For Development
- Set up testing framework
- Add more features
- Improve performance
- Enhance security
- Add integrations

Welcome to AIntegrity! We hope you build something amazing. 🎉

(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[191],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var s=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:i,asChild:o=!1,...c}=e,l=o?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,n.cn)(d({variant:t,size:i,className:r})),...c})}},811:(e,r,t)=>{"use strict";t.d(r,{NotesGrid:()=>g});var s=t(5155),a=t(2115),i=t(6874),n=t.n(i),d=t(6695),o=t(6126),c=t(285),l=t(4616),u=t(7434);function g(){let[e,r]=(0,a.useState)([]),[t,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{g()},[]);let g=async()=>{try{let e=await fetch("/api/notes");if(e.ok){let t=await e.json();r(t)}}catch(e){console.error("Error fetching notes:",e)}finally{i(!1)}};return t?(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[...Array(6)].map((e,r)=>(0,s.jsxs)(d.Zp,{className:"animate-pulse",children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-5/6"})]})})]},r))}):(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(c.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard/notes/new",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Create New Note"]})})}),0===e.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(u.A,{className:"mx-auto h-12 w-12 text-gray-400 mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No notes yet"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first note to start building your knowledge base."}),(0,s.jsx)(c.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/dashboard/notes/new",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Create Note"]})})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.map(e=>(0,s.jsx)(n(),{href:"/dashboard/notes/".concat(e.id),children:(0,s.jsxs)(d.Zp,{className:"hover:shadow-md transition-shadow cursor-pointer h-full",children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{className:"text-lg line-clamp-2",children:e.title})}),(0,s.jsxs)(d.Wu,{children:[(0,s.jsxs)("p",{className:"text-gray-600 text-sm line-clamp-3 mb-3",children:[e.content.slice(0,150),"..."]}),e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-1 mb-3",children:[e.tags.slice(0,3).map(e=>(0,s.jsx)(o.E,{variant:"secondary",className:"text-xs",children:e},e)),e.tags.length>3&&(0,s.jsxs)(o.E,{variant:"secondary",className:"text-xs",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Updated ",new Date(e.updatedAt).toLocaleDateString()]})]})]})},e.id))})]})}},6126:(e,r,t)=>{"use strict";t.d(r,{E:()=>o});var s=t(5155);t(2115);var a=t(9708),i=t(2085),n=t(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:r,variant:t,asChild:i=!1,...o}=e,c=i?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),r),...o})}},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var s=t(5155);t(2115);var a=t(9434);function i(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function n(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function c(e){let{className:r,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}},8751:(e,r,t)=>{Promise.resolve().then(t.bind(t,811))},9434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(2596),a=t(9688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}}},e=>{var r=r=>e(e.s=r);e.O(0,[352,440,441,684,358],()=>r(8751)),_N_E=e.O()}]);
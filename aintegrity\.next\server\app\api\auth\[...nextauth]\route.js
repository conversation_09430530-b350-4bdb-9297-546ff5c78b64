(()=>{var e={};e.id=14,e.ids=[14],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,r,t)=>{"use strict";t.d(r,{z:()=>i});var s=t(96330);let i=globalThis.prisma??new s.PrismaClient},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>n});var s=t(16467),i=t(36344),u=t(65752),a=t(5069);let n={adapter:(0,s.y)(a.z),providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,u.A)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET})],callbacks:{session:async({session:e,token:r})=>(e?.user&&r?.sub&&(e.user.id=r.sub),e),jwt:async({user:e,token:r})=>(e&&(r.uid=e.id),r)},session:{strategy:"jwt"},pages:{signIn:"/auth/signin"}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47568:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>c});var i=t(96559),u=t(48088),a=t(37719),n=t(19854),p=t.n(n),o=t(12909);let c=p()(o.N),d=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/auth/[...nextauth]/route",pathname:"/api/auth/[...nextauth]",filename:"route",bundlePath:"app/api/auth/[...nextauth]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\api\\auth\\[...nextauth]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:l,serverHooks:v}=d;function h(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:l})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[243,374],()=>t(47568));module.exports=s})();
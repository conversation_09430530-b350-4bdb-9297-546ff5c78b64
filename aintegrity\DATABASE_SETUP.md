# Database Setup Guide

## 🚨 Current Issue

**Error**: `Cannot fetch data from service: fetch failed`

**Cause**: The application is trying to connect to a database but the `DATABASE_URL` is not properly configured.

## ✅ Quick Fix (5 minutes)

### Step 1: Get Your Supabase Database Password

1. **Go to your Supabase project**: https://supabase.com/dashboard/projects
2. **Select your project**: `etdfnpxijvmypfgmoiup`
3. **Go to Settings > Database**
4. **Find the "Connection string" section**
5. **Copy the connection string** (it will look like):
   ```
   postgresql://postgres.etdfnpxijvmypfgmoiup:[YOUR-PASSWORD]@aws-0-us-west-1.pooler.supabase.com:6543/postgres
   ```

### Step 2: Update Your .env File

1. **Open** `aintegrity/.env`
2. **Find this line**:
   ```
   DATABASE_URL="postgresql://postgres.etdfnpxijvmypfgmoiup:[YOUR_DB_PASSWORD]@aws-0-us-west-1.pooler.supabase.com:6543/postgres?pgbouncer=true&connection_limit=1"
   ```
3. **Replace `[YOUR_DB_PASSWORD]`** with your actual database password from Supabase

### Step 3: Push Database Schema

```bash
cd aintegrity
npm run db:push
```

### Step 4: Test the Application

```bash
npm run dev
# Visit http://localhost:3000
```

## 🔧 Alternative: Use a Different Database

If you don't want to use Supabase, here are other options:

### Option A: Neon (Free PostgreSQL)
1. Go to [neon.tech](https://neon.tech)
2. Create a free account
3. Create a new database
4. Copy the connection string
5. Update `DATABASE_URL` in `.env`

### Option B: Railway (Free PostgreSQL)
1. Go to [railway.app](https://railway.app)
2. Create a new project
3. Add PostgreSQL service
4. Copy the connection string
5. Update `DATABASE_URL` in `.env`

### Option C: Local PostgreSQL
1. Install PostgreSQL locally
2. Create a database: `createdb aintegrity`
3. Update `.env`:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/aintegrity"
   ```

## 🎯 Expected Result

After setting up the database correctly:

1. **Authentication will work** - Google OAuth sign-in
2. **Notes can be saved** - Create and manage notes
3. **Chat history persists** - Conversations are saved
4. **No database errors** - Clean application startup

## 🚨 Troubleshooting

### Error: "Environment variable not found: DATABASE_URL"
- Make sure `.env` file exists in the `aintegrity` folder
- Ensure `DATABASE_URL` is properly set

### Error: "Connection refused"
- Check if your database is running
- Verify the connection string is correct
- Ensure your IP is whitelisted (for cloud databases)

### Error: "Authentication failed"
- Double-check your database password
- Ensure the username is correct
- Try regenerating the database password

## 📝 Quick Commands

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# View database in browser
npm run db:studio

# Reset database (careful!)
npm run db:push --force-reset
```

## ✅ Success Indicators

You'll know it's working when:
- ✅ `npm run dev` starts without database errors
- ✅ You can sign in with Google
- ✅ You can create and save notes
- ✅ Chat conversations are persistent

## 🆘 Need Help?

If you're still having issues:
1. Check the exact error message in the terminal
2. Verify your Supabase project is active
3. Try the database connection in Prisma Studio
4. Ensure your `.env` file has no extra spaces or quotes

**Most common fix**: Just getting the correct database password from Supabase and updating the `DATABASE_URL` in your `.env` file!

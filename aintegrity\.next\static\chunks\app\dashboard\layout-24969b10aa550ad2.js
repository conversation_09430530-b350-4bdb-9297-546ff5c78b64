(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(5155);a(2115);var r=a(9708),n=a(2085),i=a(9434);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...l}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(d({variant:a,size:n,className:t})),...l})}},4017:(e,t,a)=>{"use strict";a.d(t,{Sidebar:()=>h});var s=a(5155),r=a(6874),n=a.n(r),i=a(5695),d=a(1497),o=a(7434),l=a(4616),c=a(285),u=a(9434);let v=[{name:"Chat",href:"/dashboard",icon:d.A},{name:"Notes",href:"/dashboard/notes",icon:o.A}];function h(){let e=(0,i.usePathname)();return(0,s.jsxs)("div",{className:"w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col",children:[(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"AIntegrity"})}),(0,s.jsx)("nav",{className:"flex-1 px-4 space-y-2",children:v.map(t=>{let a=e===t.href;return(0,s.jsxs)(n(),{href:t.href,className:(0,u.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",a?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[(0,s.jsx)(t.icon,{className:"mr-3 h-5 w-5"}),t.name]},t.name)})}),(0,s.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,s.jsx)(c.$,{asChild:!0,className:"w-full",children:(0,s.jsxs)(n(),{href:"/dashboard/notes/new",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"New Note"]})})})]})}},7380:(e,t,a)=>{"use strict";a.d(t,{Header:()=>b});var s=a(5155),r=a(2108);a(2115);var n=a(4011),i=a(9434);function d(e){let{className:t,...a}=e;return(0,s.jsx)(n.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(n._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(n.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var c=a(285),u=a(8279);function v(e){let{...t}=e;return(0,s.jsx)(u.bL,{"data-slot":"dropdown-menu",...t})}function h(e){let{...t}=e;return(0,s.jsx)(u.l9,{"data-slot":"dropdown-menu-trigger",...t})}function m(e){let{className:t,sideOffset:a=4,...r}=e;return(0,s.jsx)(u.ZL,{children:(0,s.jsx)(u.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...r})})}function x(e){let{className:t,inset:a,variant:r="default",...n}=e;return(0,s.jsx)(u.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n})}var f=a(1007),g=a(4835);function b(){var e,t,a,n,i,u;let{data:b}=(0,r.useSession)();return(0,s.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{children:(0,s.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:"Dashboard"})}),(0,s.jsxs)(v,{children:[(0,s.jsx)(h,{asChild:!0,children:(0,s.jsx)(c.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(d,{className:"h-8 w-8",children:[(0,s.jsx)(o,{src:(null==b||null==(e=b.user)?void 0:e.image)||"",alt:(null==b||null==(t=b.user)?void 0:t.name)||""}),(0,s.jsx)(l,{children:(null==b||null==(n=b.user)||null==(a=n.name)?void 0:a.charAt(0))||(0,s.jsx)(f.A,{className:"h-4 w-4"})})]})})}),(0,s.jsxs)(m,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsxs)(x,{className:"flex flex-col items-start",children:[(0,s.jsx)("div",{className:"font-medium",children:null==b||null==(i=b.user)?void 0:i.name}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:null==b||null==(u=b.user)?void 0:u.email})]}),(0,s.jsxs)(x,{onClick:()=>{(0,r.signOut)({callbackUrl:"/auth/signin"})},children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Sign out"]})]})]})]})})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},9447:(e,t,a)=>{Promise.resolve().then(a.bind(a,7380)),Promise.resolve().then(a.bind(a,4017))}},e=>{var t=t=>e(e.s=t);e.O(0,[352,108,440,107,441,684,358],()=>t(9447)),_N_E=e.O()}]);
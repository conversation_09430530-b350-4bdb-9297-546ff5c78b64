(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{126:(e,t,r)=>{"use strict";r.d(t,{ChatInterface:()=>f});var a=r(5155),s=r(2115),n=r(285),i=r(2523),o=r(6695),d=r(9946);let l=(0,d.A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);var c=r(1007);let u=(0,d.A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);var h=r(9434);function f(){let[e,t]=(0,s.useState)([]),[r,d]=(0,s.useState)(""),[f,m]=(0,s.useState)(!1),[x,g]=(0,s.useState)(null),v=(0,s.useRef)(null),p=()=>{var e;null==(e=v.current)||e.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{p()},[e]);let b=async()=>{if(!r.trim()||f)return;let e={id:Date.now().toString(),content:r,role:"user",timestamp:new Date};t(t=>[...t,e]),d(""),m(!0);try{let e=await fetch("/api/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:r,chatId:x})});if(!e.ok)throw Error("Failed to send message");let a=await e.json(),s={id:(Date.now()+1).toString(),content:a.response,role:"assistant",timestamp:new Date};t(e=>[...e,s]),g(a.chatId)}catch(r){console.error("Error sending message:",r);let e={id:(Date.now()+1).toString(),content:"Sorry, there was an error processing your message. Please try again.",role:"assistant",timestamp:new Date};t(t=>[...t,e])}finally{m(!1)}};return(0,a.jsxs)("div",{className:"flex flex-col h-full max-w-4xl mx-auto p-6",children:[(0,a.jsxs)("div",{className:"flex-1 overflow-auto space-y-4 mb-4",children:[0===e.length?(0,a.jsxs)("div",{className:"text-center text-muted-foreground mt-8",children:[(0,a.jsx)(l,{className:"mx-auto h-12 w-12 mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"Start a conversation"}),(0,a.jsx)("p",{children:"Ask me anything! I can help you with questions and access your saved notes."})]}):e.map(e=>(0,a.jsxs)("div",{className:(0,h.cn)("flex gap-3","user"===e.role?"justify-end":"justify-start"),children:["assistant"===e.role&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(l,{className:"w-4 h-4 text-white"})})}),(0,a.jsx)(o.Zp,{className:(0,h.cn)("max-w-[80%] p-3","user"===e.role?"bg-primary text-primary-foreground":"bg-card text-card-foreground"),children:(0,a.jsx)("p",{className:"whitespace-pre-wrap",children:e.content})}),"user"===e.role&&(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-muted rounded-full flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"w-4 h-4 text-muted-foreground"})})})]},e.id)),f&&(0,a.jsxs)("div",{className:"flex gap-3 justify-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(l,{className:"w-4 h-4 text-white"})})}),(0,a.jsx)(o.Zp,{className:"max-w-[80%] p-3 bg-card text-card-foreground",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-muted-foreground rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-muted-foreground rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-muted-foreground rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),(0,a.jsx)("div",{ref:v})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(i.p,{value:r,onChange:e=>d(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),b())},placeholder:"Type your message...",disabled:f,className:"flex-1"}),(0,a.jsx)(n.$,{onClick:b,disabled:f||!r.trim(),children:(0,a.jsx)(u,{className:"w-4 h-4"})})]})]})}},285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(5155);r(2115);var s=r(9708),n=r(2085),i=r(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:n,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:t})),...l})}},1007:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},6695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i});var a=r(5155);r(2115);var s=r(9434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}},9434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:c="",children:u,iconNode:h,...f}=e;return(0,a.createElement)("svg",{ref:t,...l,width:s,height:s,stroke:r,strokeWidth:i?24*Number(n)/Number(s):n,className:o("lucide",c),...!u&&!d(f)&&{"aria-hidden":"true"},...f},[...h.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:d,...l}=r;return(0,a.createElement)(c,{ref:n,iconNode:t,className:o("lucide-".concat(s(i(e))),"lucide-".concat(e),d),...l})});return r.displayName=i(e),r}},9951:(e,t,r)=>{Promise.resolve().then(r.bind(r,126))}},e=>{var t=t=>e(e.s=t);e.O(0,[352,441,684,358],()=>t(9951)),_N_E=e.O()}]);
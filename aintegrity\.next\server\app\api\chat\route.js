(()=>{var e={};e.id=276,e.ids=[276],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5069:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},6140:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{POST:()=>l});var n=r(96559),a=r(48088),i=r(37719),o=r(32190),u=r(19854),c=r(12909),p=r(5069),d=r(33509);async function l(e){try{let t,r=await (0,u.getServerSession)(c.N);if(!r?.user?.email)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{message:s,chatId:n}=await e.json();if(!s)return o.NextResponse.json({error:"Message is required"},{status:400});let a=await p.z.user.findUnique({where:{email:r.user.email}});if(!a)return o.NextResponse.json({error:"User not found"},{status:404});n&&(t=await p.z.chat.findFirst({where:{id:n,userId:a.id},include:{messages:{orderBy:{createdAt:"asc"}}}})),t||(t=await p.z.chat.create({data:{userId:a.id,title:s.slice(0,50)+(s.length>50?"...":"")},include:{messages:!0}})),await p.z.message.create({data:{content:s,role:"user",chatId:t.id}});let i="";try{let e=await p.z.note.findMany({where:{userId:a.id},take:3,orderBy:{updatedAt:"desc"}});e.length>0&&(i=e.map(e=>`${e.title}: ${e.content}`).join("\n\n"))}catch(e){console.error("Error getting context:",e)}let l=t.messages.map(e=>({role:e.role,content:e.content}));l.push({role:"user",content:s});let m=await (0,d.kY)(l,i);return await p.z.message.create({data:{content:m,role:"assistant",chatId:t.id}}),o.NextResponse.json({response:m,chatId:t.id})}catch(e){return console.error("Chat API error:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/chat/route",pathname:"/api/chat",filename:"route",bundlePath:"app/api/chat/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\AIntegrity\\aintegrity\\src\\app\\api\\chat\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:x,serverHooks:g}=m;function v(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:x})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(16467),n=r(36344),a=r(65752),i=r(5069);let o={adapter:(0,s.y)(i.z),providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_ID,clientSecret:process.env.GITHUB_SECRET})],callbacks:{session:async({session:e,token:t})=>(e?.user&&t?.sub&&(e.user.id=t.sub),e),jwt:async({user:e,token:t})=>(e&&(t.uid=e.id),t)},session:{strategy:"jwt"},pages:{signIn:"/auth/signin"}}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33509:(e,t,r)=>{"use strict";r.d(t,{Lu:()=>a,kY:()=>i});var s=r(40276);if(!process.env.OPENAI_API_KEY)throw Error("Missing OPENAI_API_KEY environment variable");let n=new s.Ay({apiKey:process.env.OPENAI_API_KEY});async function a(e){return(await n.embeddings.create({model:"text-embedding-3-small",input:e})).data[0].embedding}async function i(e,t){let r=t?{role:"system",content:`You are a helpful AI assistant. Use the following context from the user's notes to inform your responses when relevant:

${t}`}:{role:"system",content:"You are a helpful AI assistant."},s=await n.chat.completions.create({model:"gpt-4o-mini",messages:[r,...e],temperature:.7,max_tokens:2e3});return s.choices[0]?.message?.content||""}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[243,706,580,641],()=>r(6140));module.exports=s})();